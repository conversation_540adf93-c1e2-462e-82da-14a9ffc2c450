<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clickup_pages', function (Blueprint $table) {
            // Page builder configuration
            $table->json('page_layout')->nullable()->after('data_sources');
            $table->json('widgets_config')->nullable()->after('page_layout');
            $table->json('global_filters')->nullable()->after('widgets_config');
            $table->string('layout_type')->default('grid')->after('global_filters');
            $table->boolean('is_published')->default(false)->after('layout_type');
            $table->timestamp('last_built_at')->nullable()->after('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clickup_pages', function (Blueprint $table) {
            $table->dropColumn([
                'page_layout',
                'widgets_config',
                'global_filters',
                'layout_type',
                'is_published',
                'last_built_at'
            ]);
        });
    }
};
