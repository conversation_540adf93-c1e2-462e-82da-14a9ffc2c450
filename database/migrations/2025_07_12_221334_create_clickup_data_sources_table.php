<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_data_sources', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Custom user-defined name
            $table->string('description')->nullable();
            $table->string('type')->default('composite'); // single, composite, custom
            $table->json('sources'); // Array of individual data sources
            $table->json('configuration')->nullable(); // Additional configuration options
            $table->boolean('enabled')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes for performance
            $table->index(['enabled', 'sort_order']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_data_sources');
    }
};
