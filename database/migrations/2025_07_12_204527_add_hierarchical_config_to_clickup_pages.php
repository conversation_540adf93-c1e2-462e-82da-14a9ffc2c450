<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clickup_pages', function (Blueprint $table) {
            // Add hierarchical configuration field
            $table->json('data_sources')->nullable()->after('widget_config');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clickup_pages', function (Blueprint $table) {
            $table->dropColumn('data_sources');
        });
    }
};
