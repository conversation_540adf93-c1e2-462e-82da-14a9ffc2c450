<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_pages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('route')->unique();
            $table->string('icon')->default('fas fa-page');
            $table->boolean('enabled')->default(true);
            $table->boolean('is_default')->default(false);
            $table->integer('sort_order')->default(0);
            $table->json('layout_config')->nullable(); // Store layout preferences
            $table->json('filter_config')->nullable(); // Store default filters
            $table->json('widget_config')->nullable(); // Store widget configurations
            $table->string('page_type')->default('custom'); // custom, tasks, progress, team, overdue
            $table->timestamps();

            // Indexes
            $table->index(['enabled', 'sort_order']);
            $table->index('page_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_pages');
    }
};
