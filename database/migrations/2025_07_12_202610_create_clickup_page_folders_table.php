<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_page_folders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clickup_page_id')->constrained('clickup_pages')->onDelete('cascade');
            $table->string('clickup_folder_id'); // ClickUp folder ID from API
            $table->string('folder_name'); // Cache the folder name for performance
            $table->boolean('enabled')->default(true);
            $table->integer('sort_order')->default(0);
            $table->json('folder_config')->nullable(); // Store folder-specific configurations
            $table->timestamps();

            // Indexes and constraints
            $table->unique(['clickup_page_id', 'clickup_folder_id']);
            $table->index(['clickup_page_id', 'enabled', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_page_folders');
    }
};
