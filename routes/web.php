<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\PluginManagementController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard routes
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Settings routes
Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
Route::post('/settings', [SettingsController::class, 'update'])->name('settings.update');

// Plugin Management routes
Route::get('/plugins', [PluginManagementController::class, 'index'])->name('plugins.manage');
Route::post('/plugins/import', [PluginManagementController::class, 'import'])->name('plugins.import');
Route::get('/plugins/download/{file}', [PluginManagementController::class, 'download'])->name('plugins.download');
Route::post('/plugins/{plugin}/toggle', [PluginManagementController::class, 'toggleActivation'])->name('plugins.toggle');
Route::get('/plugins/{plugin}/export', [PluginManagementController::class, 'export'])->name('plugins.export');
Route::get('/plugins/{plugin}/settings', [PluginManagementController::class, 'settings'])->name('plugins.settings');
Route::post('/plugins/{plugin}/settings', [PluginManagementController::class, 'updateSettings'])->name('plugins.settings.update');
