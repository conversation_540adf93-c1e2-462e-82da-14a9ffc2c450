<?php

namespace App\Contracts;

interface PluginInterface
{
    /**
     * Get the plugin name
     */
    public function getName(): string;

    /**
     * Get the plugin description
     */
    public function getDescription(): string;

    /**
     * Get the plugin version
     */
    public function getVersion(): string;

    /**
     * Register plugin routes
     */
    public function registerRoutes(): void;

    /**
     * Register dashboard widgets
     */
    public function registerWidgets(): array;

    /**
     * Register sidebar navigation items
     */
    public function registerSidebarItems(): array;

    /**
     * Get plugin configuration schema
     */
    public function getConfigSchema(): array;

    /**
     * Boot the plugin
     */
    public function boot(): void;

    /**
     * Check if plugin is enabled
     */
    public function isEnabled(): bool;

    /**
     * Run plugin migrations
     */
    public function runMigrations(): bool;

    /**
     * Create a new migration
     */
    public function createMigration(string $name): string;
}
