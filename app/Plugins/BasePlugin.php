<?php

namespace App\Plugins;

use App\Contracts\PluginInterface;
use Illuminate\Support\Facades\Route;

abstract class BasePlugin implements PluginInterface
{
    protected string $name;
    protected string $description;
    protected string $version = '1.0.0';
    protected bool $enabled = true;

    public function getName(): string
    {
        return $this->name ?? class_basename(static::class);
    }

    public function getDescription(): string
    {
        return $this->description ?? 'No description provided';
    }

    public function getVersion(): string
    {
        return $this->version;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function registerRoutes(): void
    {
        $routesPath = $this->getPluginPath() . '/routes.php';
        if (file_exists($routesPath)) {
            Route::prefix('api/plugins/' . strtolower($this->getName()))
                ->middleware(['api'])
                ->group($routesPath);
        }
    }

    public function registerWidgets(): array
    {
        return [];
    }

    public function registerSidebarItems(): array
    {
        return [];
    }

    public function getConfigSchema(): array
    {
        return [];
    }

    public function boot(): void
    {
        // Override in child classes if needed
    }

    /**
     * Get the plugin directory path
     */
    protected function getPluginPath(): string
    {
        $reflection = new \ReflectionClass(static::class);
        return dirname($reflection->getFileName());
    }

    /**
     * Get plugin configuration from plugin-specific .env file
     */
    protected function getConfig(string $key = null, $default = null)
    {
        $config = $this->loadPluginConfig();

        if ($key === null) {
            return $config;
        }

        return data_get($config, $key, $default);
    }

    /**
     * Load configuration from plugin-specific .env file
     */
    protected function loadPluginConfig(): array
    {
        $pluginPath = $this->getPluginPath();
        $envFile = $pluginPath . '/.env';

        if (!file_exists($envFile)) {
            return [];
        }

        $config = [];
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip comments
            if (strpos($line, '#') === 0) {
                continue;
            }

            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Remove quotes if present
                if (($value[0] ?? '') === '"' && ($value[-1] ?? '') === '"') {
                    $value = substr($value, 1, -1);
                } elseif (($value[0] ?? '') === "'" && ($value[-1] ?? '') === "'") {
                    $value = substr($value, 1, -1);
                }

                // Convert boolean strings
                if (strtolower($value) === 'true') {
                    $value = true;
                } elseif (strtolower($value) === 'false') {
                    $value = false;
                } elseif (is_numeric($value)) {
                    $value = is_float($value) ? (float) $value : (int) $value;
                }

                $config[strtolower($key)] = $value;
            }
        }

        return $config;
    }

    /**
     * Save configuration to plugin-specific .env file
     */
    protected function savePluginConfig(array $config): bool
    {
        $pluginPath = $this->getPluginPath();
        $envFile = $pluginPath . '/.env';

        $content = "# {$this->getName()} Plugin Configuration\n";

        foreach ($config as $key => $value) {
            $key = strtoupper($key);

            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_string($value) && (strpos($value, ' ') !== false || empty($value))) {
                $value = '"' . $value . '"';
            }

            $content .= "{$key}={$value}\n";
        }

        return file_put_contents($envFile, $content) !== false;
    }
}
