<?php

namespace App\Plugins;

use App\Contracts\PluginInterface;
use Illuminate\Support\Facades\Route;

abstract class BasePlugin implements PluginInterface
{
    protected string $name;
    protected string $description;
    protected string $version = '1.0.0';
    protected bool $enabled = true;

    public function getName(): string
    {
        $config = $this->loadPluginConfig();
        return $config['plugin_name'] ?? $this->name ?? class_basename(static::class);
    }

    public function getDescription(): string
    {
        $config = $this->loadPluginConfig();
        return $config['plugin_description'] ?? $this->description ?? 'No description provided';
    }

    public function getVersion(): string
    {
        $config = $this->loadPluginConfig();
        return $config['plugin_version'] ?? $this->version ?? '1.0.0';
    }

    public function isEnabled(): bool
    {
        $config = $this->loadPluginConfig();
        $envEnabled = $config['plugin_enabled'] ?? null;

        // Convert string values to boolean
        if ($envEnabled !== null) {
            return filter_var($envEnabled, FILTER_VALIDATE_BOOLEAN);
        }

        return $this->enabled ?? true;
    }

    public function registerRoutes(): void
    {
        $routesPath = $this->getPluginPath() . '/routes.php';
        if (file_exists($routesPath)) {
            // Register API routes
            Route::prefix('api/plugins/' . strtolower($this->getName()))
                ->middleware(['api'])
                ->group($routesPath);
        }

        // Register web routes if they exist
        $webRoutesPath = $this->getPluginPath() . '/web-routes.php';
        if (file_exists($webRoutesPath)) {
            Route::prefix(strtolower($this->getName()))
                ->middleware(['web'])
                ->group($webRoutesPath);
        }
    }

    public function registerWidgets(): array
    {
        return [];
    }

    public function registerSidebarItems(): array
    {
        return [];
    }

    public function getConfigSchema(): array
    {
        return [];
    }

    public function boot(): void
    {
        // Register plugin views
        $this->registerViews();

        // Override in child classes if needed
    }

    /**
     * Register plugin views with Laravel's view system
     */
    protected function registerViews(): void
    {
        $viewsPath = $this->getPluginPath() . '/views';

        if (file_exists($viewsPath)) {
            $pluginName = strtolower($this->getName());

            // Register the view namespace
            view()->addNamespace($pluginName, $viewsPath);

            // Also add to view finder for fallback
            view()->getFinder()->addLocation($viewsPath);
        }
    }

    /**
     * Get the plugin directory path
     */
    protected function getPluginPath(): string
    {
        $reflection = new \ReflectionClass(static::class);
        return dirname($reflection->getFileName());
    }

    /**
     * Get plugin configuration from plugin-specific .env file
     */
    protected function getConfig(string $key = null, $default = null)
    {
        $config = $this->loadPluginConfig();

        if ($key === null) {
            return $config;
        }

        return data_get($config, $key, $default);
    }

    /**
     * Load configuration from plugin-specific .env file
     */
    protected function loadPluginConfig(): array
    {
        $pluginPath = $this->getPluginPath();
        $envFile = $pluginPath . '/.env';

        if (!file_exists($envFile)) {
            return [];
        }

        $config = [];
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip comments
            if (strpos($line, '#') === 0) {
                continue;
            }

            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Remove quotes if present
                if (($value[0] ?? '') === '"' && ($value[-1] ?? '') === '"') {
                    $value = substr($value, 1, -1);
                } elseif (($value[0] ?? '') === "'" && ($value[-1] ?? '') === "'") {
                    $value = substr($value, 1, -1);
                }

                // Convert boolean strings
                if (strtolower($value) === 'true') {
                    $value = true;
                } elseif (strtolower($value) === 'false') {
                    $value = false;
                } elseif (is_numeric($value)) {
                    $value = is_float($value) ? (float) $value : (int) $value;
                }

                $config[strtolower($key)] = $value;
            }
        }

        return $config;
    }

    /**
     * Save configuration to plugin-specific .env file
     */
    protected function savePluginConfig(array $config): bool
    {
        $pluginPath = $this->getPluginPath();
        $envFile = $pluginPath . '/.env';

        // Load existing configuration to preserve other values
        $existingConfig = $this->loadPluginConfig();

        // Merge new config with existing config
        $mergedConfig = array_merge($existingConfig, $config);

        // Read existing .env file to preserve comments and structure
        $existingContent = '';
        if (file_exists($envFile)) {
            $existingContent = file_get_contents($envFile);
        }

        // Update only the changed values while preserving structure
        $updatedContent = $existingContent;

        foreach ($config as $key => $value) {
            $key = strtoupper($key);

            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_string($value) && (strpos($value, ' ') !== false || empty($value))) {
                $value = '"' . $value . '"';
            }

            // Pattern to match the key=value line
            $pattern = '/^' . preg_quote($key, '/') . '=.*$/m';
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $updatedContent)) {
                // Update existing line
                $updatedContent = preg_replace($pattern, $replacement, $updatedContent);
            } else {
                // Add new line after the plugin metadata section
                $metadataEndPattern = '/^PLUGIN_ENABLED=.*$/m';
                if (preg_match($metadataEndPattern, $updatedContent)) {
                    $updatedContent = preg_replace($metadataEndPattern, "$0\n{$replacement}", $updatedContent);
                } else {
                    // Fallback: append to end
                    $updatedContent .= "\n{$replacement}";
                }
            }
        }

        return file_put_contents($envFile, $updatedContent) !== false;
    }

    /**
     * Get plugin migrations directory
     */
    protected function getMigrationsPath(): string
    {
        return $this->getPluginPath() . '/migrations';
    }

    /**
     * Run plugin migrations
     */
    public function runMigrations(): bool
    {
        $migrationsPath = $this->getMigrationsPath();

        if (!is_dir($migrationsPath)) {
            return true; // No migrations to run
        }

        try {
            $migrationFiles = glob($migrationsPath . '/*.php');

            foreach ($migrationFiles as $file) {
                $this->runMigrationFile($file);
            }

            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to run migrations for plugin {$this->getName()}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Run a single migration file
     */
    protected function runMigrationFile(string $filePath): void
    {
        $fileName = basename($filePath, '.php');

        // Check if migration has already been run
        if ($this->migrationHasRun($fileName)) {
            return;
        }

        // Include and execute the migration
        require_once $filePath;

        // Extract class name from file name (Laravel convention)
        $className = $this->getMigrationClassName($fileName);

        if (class_exists($className)) {
            $migration = new $className();

            if (method_exists($migration, 'up')) {
                $migration->up();
                $this->markMigrationAsRun($fileName);
            }
        }
    }

    /**
     * Check if migration has already been run
     */
    protected function migrationHasRun(string $fileName): bool
    {
        $tableName = $this->getPluginMigrationsTable();

        // Create migrations table if it doesn't exist
        $this->createPluginMigrationsTable();

        return \DB::table($tableName)
            ->where('migration', $fileName)
            ->where('plugin', $this->getName())
            ->exists();
    }

    /**
     * Mark migration as run
     */
    protected function markMigrationAsRun(string $fileName): void
    {
        $tableName = $this->getPluginMigrationsTable();

        \DB::table($tableName)->insert([
            'plugin' => $this->getName(),
            'migration' => $fileName,
            'batch' => $this->getNextBatchNumber(),
            'created_at' => now(),
        ]);
    }

    /**
     * Get plugin migrations table name
     */
    protected function getPluginMigrationsTable(): string
    {
        return 'plugin_migrations';
    }

    /**
     * Create plugin migrations table
     */
    protected function createPluginMigrationsTable(): void
    {
        $tableName = $this->getPluginMigrationsTable();

        if (!\Schema::hasTable($tableName)) {
            \Schema::create($tableName, function ($table) {
                $table->id();
                $table->string('plugin');
                $table->string('migration');
                $table->integer('batch');
                $table->timestamp('created_at');

                $table->index(['plugin', 'migration']);
            });
        }
    }

    /**
     * Get next batch number
     */
    protected function getNextBatchNumber(): int
    {
        $tableName = $this->getPluginMigrationsTable();

        $lastBatch = \DB::table($tableName)->max('batch');
        return $lastBatch ? $lastBatch + 1 : 1;
    }

    /**
     * Get migration class name from file name
     */
    protected function getMigrationClassName(string $fileName): string
    {
        // Remove .php extension if present
        $fileName = str_replace('.php', '', $fileName);

        // Remove timestamp prefix and convert to PascalCase
        $parts = explode('_', $fileName);

        // Remove timestamp (first 4 parts: YYYY_MM_DD_HHMMSS)
        if (count($parts) > 4 && is_numeric($parts[0])) {
            $parts = array_slice($parts, 4);
        }

        $className = '';
        foreach ($parts as $part) {
            $className .= ucfirst($part);
        }

        return $className;
    }

    /**
     * Create a new migration file
     */
    public function createMigration(string $name): string
    {
        $migrationsPath = $this->getMigrationsPath();

        // Create migrations directory if it doesn't exist
        if (!is_dir($migrationsPath)) {
            mkdir($migrationsPath, 0755, true);
        }

        $timestamp = date('Y_m_d_His');
        $fileName = "{$timestamp}_{$name}.php";
        $filePath = $migrationsPath . '/' . $fileName;

        $className = $this->getMigrationClassName($fileName);

        $stub = $this->getMigrationStub($className);

        file_put_contents($filePath, $stub);

        return $filePath;
    }

    /**
     * Get migration stub content
     */
    protected function getMigrationStub(string $className): string
    {
        return "<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class {$className} extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add your migration logic here
        // Example:
        // Schema::create('example_table', function (Blueprint \$table) {
        //     \$table->id();
        //     \$table->string('name');
        //     \$table->timestamps();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add your rollback logic here
        // Example:
        // Schema::dropIfExists('example_table');
    }
}
";
    }
}
