<?php

namespace App\Plugins\TaqnyatAdmin\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class TaqnyatAdminController extends Controller
{
    public function getMetrics(): JsonResponse
    {
        return response()->json([
            'total_clients' => 150,
            'active_subscriptions' => 120,
            'monthly_revenue' => 45000,
            'new_leads' => 25,
            'conversion_rate' => 18.5
        ]);
    }

    public function getClients(): JsonResponse
    {
        return response()->json([
            'clients' => [
                ['id' => 1, 'name' => 'Acme Corp', 'status' => 'active', 'subscription' => 'Premium'],
                ['id' => 2, 'name' => 'Tech Solutions', 'status' => 'active', 'subscription' => 'Standard'],
                ['id' => 3, 'name' => 'Digital Agency', 'status' => 'pending', 'subscription' => 'Basic'],
            ],
            'total' => 150
        ]);
    }

    public function getSubscriptions(): JsonResponse
    {
        return response()->json([
            'subscriptions' => [
                ['id' => 1, 'client' => 'Acme Corp', 'plan' => 'Premium', 'status' => 'active', 'amount' => 999],
                ['id' => 2, 'client' => 'Tech Solutions', 'plan' => 'Standard', 'status' => 'active', 'amount' => 499],
                ['id' => 3, 'client' => 'Digital Agency', 'plan' => 'Basic', 'status' => 'trial', 'amount' => 199],
            ],
            'total' => 120
        ]);
    }

    public function getLeads(): JsonResponse
    {
        return response()->json([
            'leads' => [
                ['id' => 1, 'name' => 'Startup Inc', 'source' => 'Website', 'status' => 'qualified'],
                ['id' => 2, 'name' => 'Enterprise Ltd', 'source' => 'Referral', 'status' => 'contacted'],
                ['id' => 3, 'name' => 'SMB Company', 'source' => 'Social Media', 'status' => 'new'],
            ],
            'total' => 25
        ]);
    }

    public function getInvoices(): JsonResponse
    {
        return response()->json([
            'invoices' => [
                ['id' => 1, 'client' => 'Acme Corp', 'amount' => 999, 'status' => 'paid', 'due_date' => '2025-07-15'],
                ['id' => 2, 'client' => 'Tech Solutions', 'amount' => 499, 'status' => 'pending', 'due_date' => '2025-07-20'],
                ['id' => 3, 'client' => 'Digital Agency', 'amount' => 199, 'status' => 'overdue', 'due_date' => '2025-07-10'],
            ],
            'total' => 75
        ]);
    }

    public function getRevenueAnalytics(): JsonResponse
    {
        return response()->json([
            'monthly_revenue' => [
                ['month' => 'Jan', 'revenue' => 42000],
                ['month' => 'Feb', 'revenue' => 38000],
                ['month' => 'Mar', 'revenue' => 45000],
                ['month' => 'Apr', 'revenue' => 41000],
                ['month' => 'May', 'revenue' => 47000],
                ['month' => 'Jun', 'revenue' => 44000],
                ['month' => 'Jul', 'revenue' => 45000],
            ],
            'growth_rate' => 12.5,
            'total_revenue' => 302000
        ]);
    }

    public function testConnection(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => 'TaqnyatAdmin API connection successful',
            'timestamp' => now()->toISOString()
        ]);
    }
}
