<?php

namespace App\Plugins\TaqnyatAdmin;

use App\Plugins\BasePlugin;

class TaqnyatAdminPlugin extends BasePlugin
{
    protected string $name = 'TaqnyatAdmin';
    protected string $description = 'Internal system integration for clients, subscriptions, leads, and invoices';
    protected string $version = '1.0.0';

    public function boot(): void
    {
        // Register any additional services or bindings here
    }

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'clients_overview',
                'title' => 'Clients Overview',
                'component' => 'TaqnyatAdminClientsWidget',
                'size' => 'medium',
                'order' => 1,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'revenue_metrics',
                'title' => 'Revenue Metrics',
                'component' => 'TaqnyatAdminRevenueWidget',
                'size' => 'large',
                'order' => 2,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'churn_analysis',
                'title' => 'Churn Analysis',
                'component' => 'TaqnyatAdminChurnWidget',
                'size' => 'medium',
                'order' => 3,
            ],
        ];
    }

    public function registerSidebarItems(): array
    {
        return [
            [
                'title' => 'TaqnyatAdmin',
                'icon' => 'fas fa-building',
                'children' => [
                    [
                        'title' => 'Clients',
                        'url' => '/taqnyat/clients',
                        'icon' => 'fas fa-users',
                    ],
                    [
                        'title' => 'Subscriptions',
                        'url' => '/taqnyat/subscriptions',
                        'icon' => 'fas fa-credit-card',
                    ],
                    [
                        'title' => 'Leads',
                        'url' => '/taqnyat/leads',
                        'icon' => 'fas fa-user-plus',
                    ],
                    [
                        'title' => 'Invoices',
                        'url' => '/taqnyat/invoices',
                        'icon' => 'fas fa-file-invoice',
                    ],
                    [
                        'title' => 'Analytics',
                        'url' => '/taqnyat/analytics',
                        'icon' => 'fas fa-chart-bar',
                    ],
                ],
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_endpoint' => [
                'type' => 'string',
                'label' => 'API Endpoint',
                'description' => 'The API endpoint for TaqnyatAdmin system',
                'required' => false,
                'default' => 'https://api.taqnyat.local',
            ],
            'api_key' => [
                'type' => 'string',
                'label' => 'API Key',
                'description' => 'API key for authentication',
                'required' => false,
                'sensitive' => true,
            ],
            'sync_interval' => [
                'type' => 'integer',
                'label' => 'Sync Interval (minutes)',
                'description' => 'How often to sync data from the system',
                'required' => false,
                'default' => 60,
            ],
        ];
    }
}
