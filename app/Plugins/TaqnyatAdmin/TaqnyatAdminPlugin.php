<?php

namespace App\Plugins\TaqnyatAdmin;

use App\Plugins\BasePlugin;

class TaqnyatAdminPlugin extends BasePlugin
{
    protected string $name = 'TaqnyatAdmin';
    protected string $description = 'Customer relationship management and business analytics integration';
    protected string $version = '1.0.0';

    public function boot(): void
    {
        // Register any additional services or bindings here
    }

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'client_metrics',
                'title' => 'Client Metrics',
                'component' => 'ClientMetrics',
                'size' => 'large',
                'order' => 1,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'subscription_overview',
                'title' => 'Subscription Overview',
                'component' => 'SubscriptionOverview',
                'size' => 'medium',
                'order' => 2,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'revenue_analytics',
                'title' => 'Revenue Analytics',
                'component' => 'RevenueAnalytics',
                'size' => 'large',
                'order' => 3,
            ],
        ];
    }

    public function registerSidebarItems(): array
    {
        return [
            [
                'title' => 'Clients',
                'icon' => 'fas fa-users',
                'url' => '/taqnyat/clients',
            ],
            [
                'title' => 'Subscriptions',
                'icon' => 'fas fa-credit-card',
                'url' => '/taqnyat/subscriptions',
            ],
            [
                'title' => 'Leads',
                'icon' => 'fas fa-user-plus',
                'url' => '/taqnyat/leads',
            ],
            [
                'title' => 'Invoices',
                'icon' => 'fas fa-file-invoice',
                'url' => '/taqnyat/invoices',
            ],
            [
                'title' => 'Analytics',
                'icon' => 'fas fa-chart-bar',
                'url' => '/taqnyat/analytics',
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_endpoint' => [
                'type' => 'string',
                'label' => 'API Endpoint',
                'description' => 'TaqnyatAdmin API endpoint URL',
                'required' => true,
            ],
            'api_key' => [
                'type' => 'string',
                'label' => 'API Key',
                'description' => 'Your TaqnyatAdmin API key',
                'required' => true,
            ],
            'sync_interval' => [
                'type' => 'number',
                'label' => 'Sync Interval (minutes)',
                'description' => 'How often to sync data from TaqnyatAdmin',
                'default' => 60,
                'required' => false,
            ],
        ];
    }
}
