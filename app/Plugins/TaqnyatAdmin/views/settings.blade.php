<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">TaqnyatAdmin Settings</h3>
                <p class="text-sm text-gray-600">Configure your TaqnyatAdmin integration settings</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v1.0.0
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                </span>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('plugins.settings.update', 'TaqnyatAdmin') }}" class="p-6">
        @csrf
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label for="taqnyat_api_endpoint" class="block text-sm font-medium text-gray-700">
                    API Endpoint
                </label>
                <input type="text"
                       id="taqnyat_api_endpoint"
                       name="api_endpoint"
                       value="{{ old('api_endpoint', $config['api_endpoint'] ?? 'https://api.taqnyat.local') }}"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">The API endpoint for TaqnyatAdmin system</p>
            </div>

            <div class="space-y-2">
                <label for="taqnyat_api_key" class="block text-sm font-medium text-gray-700">
                    API Key
                </label>
                <input type="password"
                       id="taqnyat_api_key"
                       name="api_key"
                       value="{{ old('api_key', $config['api_key'] ?? '') }}"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">API key for authentication</p>
            </div>

            <div class="space-y-2">
                <label for="taqnyat_sync_interval" class="block text-sm font-medium text-gray-700">
                    Sync Interval (minutes)
                </label>
                <input type="number"
                       id="taqnyat_sync_interval"
                       name="sync_interval"
                       value="{{ old('sync_interval', $config['sync_interval'] ?? 60) }}"
                       min="1"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">How often to sync data from the system</p>
            </div>

            <div class="space-y-2">
                <label class="flex items-center">
                    <input type="checkbox"
                           name="plugin_enabled"
                           value="1"
                           {{ ($config['plugin_enabled'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Enable Plugin</span>
                </label>
                <p class="text-xs text-gray-500">Enable or disable the TaqnyatAdmin plugin</p>
            </div>
        </div>

        <div class="mt-6 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <button type="button" 
                        onclick="testConnection()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <div id="connection-status" class="text-sm"></div>
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
async function testConnection() {
    const statusDiv = document.getElementById('connection-status');
    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-600"></i> Testing...';
    
    try {
        const response = await fetch('/api/plugins/taqnyatadmin/metrics');
        const data = await response.json();
        
        if (data.success) {
            statusDiv.innerHTML = '<i class="fas fa-check-circle text-green-600"></i> Connection successful';
        } else {
            statusDiv.innerHTML = '<i class="fas fa-exclamation-circle text-red-600"></i> Connection failed';
        }
    } catch (error) {
        statusDiv.innerHTML = '<i class="fas fa-times-circle text-red-600"></i> Connection error';
    }
    
    setTimeout(() => {
        statusDiv.innerHTML = '';
    }, 5000);
}
</script>
