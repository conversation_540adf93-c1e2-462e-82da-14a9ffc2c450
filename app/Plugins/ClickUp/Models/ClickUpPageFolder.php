<?php

namespace App\Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClickUpPageFolder extends Model
{
    protected $table = 'clickup_page_folders';

    protected $fillable = [
        'clickup_page_id',
        'clickup_folder_id',
        'folder_name',
        'enabled',
        'sort_order',
        'folder_config'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'sort_order' => 'integer',
        'folder_config' => 'array'
    ];

    /**
     * Relationship: Folder belongs to a page
     */
    public function page(): BelongsTo
    {
        return $this->belongsTo(ClickUpPage::class, 'clickup_page_id');
    }

    /**
     * Scope: Only enabled folders
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope: Order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('folder_name');
    }

    /**
     * Get folder configuration with defaults
     */
    public function getFolderConfig()
    {
        return array_merge([
            'show_in_overview' => true,
            'show_progress' => true,
            'show_lists' => true,
            'color' => '#10B981',
            'icon' => 'fas fa-folder',
            'weight' => 1.0 // For weighted calculations
        ], $this->folder_config ?? []);
    }

    /**
     * Update the cached folder name
     */
    public function updateFolderName(string $name)
    {
        $this->update(['folder_name' => $name]);
    }

    /**
     * Check if this folder is enabled for the page
     */
    public function isEnabled()
    {
        return $this->enabled && $this->page->enabled;
    }
}
