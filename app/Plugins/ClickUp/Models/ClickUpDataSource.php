<?php

namespace App\Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClickUpDataSource extends Model
{
    use HasFactory;

    protected $table = 'clickup_data_sources';

    protected $fillable = [
        'name',
        'description',
        'type',
        'sources',
        'configuration',
        'enabled',
        'sort_order'
    ];

    protected $casts = [
        'sources' => 'array',
        'configuration' => 'array',
        'enabled' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Data source types
     */
    const TYPE_SINGLE = 'single';
    const TYPE_COMPOSITE = 'composite';
    const TYPE_CUSTOM = 'custom';

    /**
     * Get all enabled data sources ordered by sort order
     */
    public static function getEnabled(): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('enabled', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get data sources by type
     */
    public static function getByType(string $type): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('enabled', true)
            ->where('type', $type)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get individual sources from this data source
     */
    public function getIndividualSources(): array
    {
        return $this->sources ?? [];
    }

    /**
     * Get source count
     */
    public function getSourceCount(): int
    {
        return count($this->getIndividualSources());
    }

    /**
     * Check if this is a composite data source
     */
    public function isComposite(): bool
    {
        return $this->type === self::TYPE_COMPOSITE && $this->getSourceCount() > 1;
    }

    /**
     * Check if this is a single data source
     */
    public function isSingle(): bool
    {
        return $this->type === self::TYPE_SINGLE || $this->getSourceCount() === 1;
    }

    /**
     * Get configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->update(['configuration' => $config]);
    }

    /**
     * Add a source to this data source
     */
    public function addSource(array $source): void
    {
        $sources = $this->getIndividualSources();
        
        // Check if source already exists
        foreach ($sources as $existingSource) {
            if ($existingSource['list_id'] === $source['list_id']) {
                return; // Source already exists
            }
        }
        
        $sources[] = $source;
        $this->update(['sources' => $sources]);
        
        // Update type based on source count
        $this->updateType();
    }

    /**
     * Remove a source from this data source
     */
    public function removeSource(string $listId): bool
    {
        $sources = $this->getIndividualSources();
        $originalCount = count($sources);
        
        $sources = array_filter($sources, function($source) use ($listId) {
            return $source['list_id'] !== $listId;
        });
        
        if (count($sources) < $originalCount) {
            $this->update(['sources' => array_values($sources)]);
            $this->updateType();
            return true;
        }
        
        return false;
    }

    /**
     * Update type based on source count
     */
    protected function updateType(): void
    {
        $count = $this->getSourceCount();
        
        if ($count === 0) {
            $this->update(['enabled' => false]);
        } elseif ($count === 1) {
            $this->update(['type' => self::TYPE_SINGLE]);
        } else {
            $this->update(['type' => self::TYPE_COMPOSITE]);
        }
    }

    /**
     * Get display information for this data source
     */
    public function getDisplayInfo(): array
    {
        $sources = $this->getIndividualSources();
        $sourceCount = count($sources);
        
        if ($sourceCount === 0) {
            return [
                'name' => $this->name,
                'description' => 'No sources configured',
                'type' => $this->type,
                'source_count' => 0,
                'sources_summary' => 'Empty'
            ];
        }
        
        if ($sourceCount === 1) {
            $source = $sources[0];
            return [
                'name' => $this->name,
                'description' => $this->description ?: $source['list_name'],
                'type' => $this->type,
                'source_count' => 1,
                'sources_summary' => $source['space_name'] . ' > ' . $source['folder_name'] . ' > ' . $source['list_name']
            ];
        }
        
        // Composite data source
        $spaces = array_unique(array_column($sources, 'space_name'));
        $folders = array_unique(array_column($sources, 'folder_name'));
        $lists = array_column($sources, 'list_name');
        
        $summary = '';
        if (count($spaces) === 1) {
            $summary = $spaces[0];
            if (count($folders) === 1) {
                $summary .= ' > ' . $folders[0];
            }
            $summary .= ' (' . $sourceCount . ' lists)';
        } else {
            $summary = count($spaces) . ' spaces, ' . $sourceCount . ' lists';
        }
        
        return [
            'name' => $this->name,
            'description' => $this->description ?: $summary,
            'type' => $this->type,
            'source_count' => $sourceCount,
            'sources_summary' => $summary,
            'spaces' => $spaces,
            'folders' => $folders,
            'lists' => $lists
        ];
    }

    /**
     * Validate that all sources are accessible
     */
    public function validateSources(): array
    {
        $sources = $this->getIndividualSources();
        $errors = [];
        
        foreach ($sources as $index => $source) {
            if (empty($source['list_id'])) {
                $errors[] = "Source {$index}: Missing list ID";
            }
            if (empty($source['list_name'])) {
                $errors[] = "Source {$index}: Missing list name";
            }
            if (empty($source['space_id'])) {
                $errors[] = "Source {$index}: Missing space ID";
            }
        }
        
        return $errors;
    }

    /**
     * Get unique identifier for this data source
     */
    public function getUniqueId(): string
    {
        return 'custom_' . $this->id;
    }

    /**
     * Convert to array format compatible with existing system
     */
    public function toCompatibleArray(): array
    {
        return [
            'id' => $this->getUniqueId(),
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type,
            'source_count' => $this->getSourceCount(),
            'sources' => $this->getIndividualSources(),
            'display_info' => $this->getDisplayInfo(),
            'enabled' => $this->enabled,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString()
        ];
    }
}
