<?php

namespace App\Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClickUpPageList extends Model
{
    protected $table = 'clickup_page_lists';

    protected $fillable = [
        'clickup_page_id',
        'clickup_list_id',
        'list_name',
        'enabled',
        'sort_order',
        'list_config'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'sort_order' => 'integer',
        'list_config' => 'array'
    ];

    /**
     * Relationship: List belongs to a page
     */
    public function page(): BelongsTo
    {
        return $this->belongsTo(ClickUpPage::class, 'clickup_page_id');
    }

    /**
     * Scope: Only enabled lists
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope: Order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('list_name');
    }

    /**
     * Get list configuration with defaults
     */
    public function getListConfig()
    {
        return array_merge([
            'show_in_overview' => true,
            'show_progress' => true,
            'show_tasks' => true,
            'color' => '#3B82F6',
            'icon' => 'fas fa-list',
            'weight' => 1.0 // For weighted calculations
        ], $this->list_config ?? []);
    }

    /**
     * Update the cached list name
     */
    public function updateListName(string $name)
    {
        $this->update(['list_name' => $name]);
    }

    /**
     * Check if this list is enabled for the page
     */
    public function isEnabled()
    {
        return $this->enabled && $this->page->enabled;
    }
}
