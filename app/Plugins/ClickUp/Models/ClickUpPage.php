<?php

namespace App\Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ClickUpPage extends Model
{
    protected $table = 'clickup_pages';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'route',
        'icon',
        'enabled',
        'is_default',
        'sort_order',
        'layout_config',
        'filter_config',
        'widget_config',
        'page_type',
        'data_sources',
        'page_layout',
        'widgets_config',
        'global_filters',
        'layout_type',
        'is_published',
        'last_built_at'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'is_default' => 'boolean',
        'layout_config' => 'array',
        'filter_config' => 'array',
        'widget_config' => 'array',
        'data_sources' => 'array',
        'page_layout' => 'array',
        'widgets_config' => 'array',
        'global_filters' => 'array',
        'is_published' => 'boolean',
        'last_built_at' => 'datetime',
        'sort_order' => 'integer'
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug from name if not provided
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->name);
            }

            // Auto-generate route if not provided
            if (empty($page->route)) {
                $page->route = 'clickup.' . $page->slug;
            }
        });

        // Ensure only one default page
        static::saving(function ($page) {
            if ($page->is_default) {
                static::where('id', '!=', $page->id)->update(['is_default' => false]);
            }
        });
    }

    /**
     * Relationship: Page has many lists
     */
    public function lists(): HasMany
    {
        return $this->hasMany(ClickUpPageList::class, 'clickup_page_id')->orderBy('sort_order');
    }

    /**
     * Relationship: Page has many folders
     */
    public function folders(): HasMany
    {
        return $this->hasMany(ClickUpPageFolder::class, 'clickup_page_id')->orderBy('sort_order');
    }

    /**
     * Scope: Only enabled pages
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope: Order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the default page
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Get enabled lists for this page
     */
    public function getEnabledLists()
    {
        return $this->lists()->where('enabled', true)->get();
    }

    /**
     * Get enabled folders for this page
     */
    public function getEnabledFolders()
    {
        return $this->folders()->where('enabled', true)->get();
    }

    /**
     * Get the full route name for this page
     */
    public function getRouteAttribute($value)
    {
        return $value ?: 'clickup.' . $this->slug;
    }

    /**
     * Get the URL for this page
     */
    public function getUrlAttribute()
    {
        return route($this->route);
    }

    /**
     * Check if this page has any associated lists
     */
    public function hasLists()
    {
        return $this->lists()->exists();
    }

    /**
     * Check if this page has any associated folders
     */
    public function hasFolders()
    {
        return $this->folders()->exists();
    }

    /**
     * Get layout configuration with defaults
     */
    public function getLayoutConfig()
    {
        return array_merge([
            'columns' => 2,
            'show_charts' => true,
            'show_filters' => true,
            'show_stats' => true,
            'card_size' => 'medium'
        ], $this->layout_config ?? []);
    }

    /**
     * Get filter configuration with defaults
     */
    public function getFilterConfig()
    {
        return array_merge([
            'default_status' => 'all',
            'default_assignee' => 'all',
            'default_priority' => 'all',
            'show_completed' => true,
            'date_range' => 30
        ], $this->filter_config ?? []);
    }

    /**
     * Get widget configuration with defaults
     */
    public function getWidgetConfig()
    {
        return array_merge([
            'show_progress_chart' => true,
            'show_status_distribution' => true,
            'show_team_performance' => true,
            'show_overdue_alerts' => true,
            'refresh_interval' => 300 // 5 minutes
        ], $this->widget_config ?? []);
    }

    /**
     * Get data sources with defaults
     */
    public function getDataSources()
    {
        return $this->data_sources ?? [];
    }

    /**
     * Add a data source configuration
     */
    public function addDataSource(string $spaceId, string $spaceName, string $folderId, string $folderName, string $listId, string $listName): void
    {
        $dataSources = $this->getDataSources();

        $dataSources[] = [
            'space_id' => $spaceId,
            'space_name' => $spaceName,
            'folder_id' => $folderId,
            'folder_name' => $folderName,
            'list_id' => $listId,
            'list_name' => $listName,
            'enabled' => true,
            'created_at' => now()->toISOString()
        ];

        $this->update(['data_sources' => $dataSources]);
    }

    /**
     * Remove a data source by index
     */
    public function removeDataSource(int $index): void
    {
        $dataSources = $this->getDataSources();

        if (isset($dataSources[$index])) {
            unset($dataSources[$index]);
            $this->update(['data_sources' => array_values($dataSources)]);
        }
    }

    /**
     * Get all unique space IDs from data sources
     */
    public function getUniqueSpaceIds(): array
    {
        $dataSources = $this->getDataSources();
        return array_unique(array_column($dataSources, 'space_id'));
    }

    /**
     * Get all unique folder IDs from data sources
     */
    public function getUniqueFolderIds(): array
    {
        $dataSources = $this->getDataSources();
        return array_unique(array_column($dataSources, 'folder_id'));
    }

    /**
     * Get all unique list IDs from data sources
     */
    public function getUniqueListIds(): array
    {
        $dataSources = $this->getDataSources();
        return array_unique(array_column($dataSources, 'list_id'));
    }

    /**
     * Get data sources grouped by space
     */
    public function getDataSourcesBySpace(): array
    {
        $dataSources = $this->getDataSources();
        $grouped = [];

        foreach ($dataSources as $source) {
            $spaceId = $source['space_id'];
            if (!isset($grouped[$spaceId])) {
                $grouped[$spaceId] = [
                    'space_id' => $spaceId,
                    'space_name' => $source['space_name'],
                    'folders' => []
                ];
            }

            $folderId = $source['folder_id'];
            if (!isset($grouped[$spaceId]['folders'][$folderId])) {
                $grouped[$spaceId]['folders'][$folderId] = [
                    'folder_id' => $folderId,
                    'folder_name' => $source['folder_name'],
                    'lists' => []
                ];
            }

            $grouped[$spaceId]['folders'][$folderId]['lists'][] = [
                'list_id' => $source['list_id'],
                'list_name' => $source['list_name'],
                'enabled' => $source['enabled'] ?? true
            ];
        }

        return $grouped;
    }

    /**
     * Validate data sources structure
     */
    public function validateDataSources(): array
    {
        $dataSources = $this->getDataSources();
        $errors = [];

        foreach ($dataSources as $index => $source) {
            if (empty($source['space_id'])) {
                $errors[] = "Data source {$index}: Space ID is required";
            }
            if (empty($source['folder_id'])) {
                $errors[] = "Data source {$index}: Folder ID is required";
            }
            if (empty($source['list_id'])) {
                $errors[] = "Data source {$index}: List ID is required";
            }
        }

        return $errors;
    }

    /**
     * Page Builder Methods
     */

    /**
     * Get page layout with defaults
     */
    public function getPageLayout(): array
    {
        return array_merge([
            'type' => 'grid',
            'columns' => 12,
            'rows' => [],
            'responsive' => true,
            'gap' => 4
        ], $this->page_layout ?? []);
    }

    /**
     * Get widgets configuration with defaults
     */
    public function getWidgetsConfig(): array
    {
        return $this->widgets_config ?? [];
    }

    /**
     * Get global filters with defaults
     */
    public function getGlobalFilters(): array
    {
        return array_merge([
            'date_range' => null,
            'status_filter' => [],
            'assignee_filter' => [],
            'priority_filter' => [],
            'custom_filters' => []
        ], $this->global_filters ?? []);
    }

    /**
     * Add a widget to the page
     */
    public function addWidget(array $widgetConfig): void
    {
        $widgets = $this->getWidgetsConfig();
        $widgetConfig['id'] = $widgetConfig['id'] ?? uniqid('widget_');
        $widgetConfig['created_at'] = now()->toISOString();

        $widgets[] = $widgetConfig;
        $this->update(['widgets_config' => $widgets]);
    }

    /**
     * Update a widget configuration
     */
    public function updateWidget(string $widgetId, array $config): bool
    {
        $widgets = $this->getWidgetsConfig();

        foreach ($widgets as &$widget) {
            if ($widget['id'] === $widgetId) {
                $widget = array_merge($widget, $config);
                $widget['updated_at'] = now()->toISOString();
                $this->update(['widgets_config' => $widgets]);
                return true;
            }
        }

        return false;
    }

    /**
     * Remove a widget from the page
     */
    public function removeWidget(string $widgetId): bool
    {
        $widgets = $this->getWidgetsConfig();
        $originalCount = count($widgets);

        $widgets = array_filter($widgets, function($widget) use ($widgetId) {
            return $widget['id'] !== $widgetId;
        });

        if (count($widgets) < $originalCount) {
            $this->update(['widgets_config' => array_values($widgets)]);
            return true;
        }

        return false;
    }

    /**
     * Get widgets by type
     */
    public function getWidgetsByType(string $type): array
    {
        $widgets = $this->getWidgetsConfig();
        return array_filter($widgets, function($widget) use ($type) {
            return ($widget['type'] ?? '') === $type;
        });
    }

    /**
     * Get widgets for a specific data source
     */
    public function getWidgetsForDataSource(string $dataSourceId): array
    {
        $widgets = $this->getWidgetsConfig();
        return array_filter($widgets, function($widget) use ($dataSourceId) {
            return ($widget['data_source_id'] ?? '') === $dataSourceId;
        });
    }

    /**
     * Update page layout
     */
    public function updatePageLayout(array $layout): void
    {
        $this->update([
            'page_layout' => $layout,
            'last_built_at' => now()
        ]);
    }

    /**
     * Publish the page
     */
    public function publish(): void
    {
        $this->update([
            'is_published' => true,
            'last_built_at' => now()
        ]);
    }

    /**
     * Unpublish the page
     */
    public function unpublish(): void
    {
        $this->update(['is_published' => false]);
    }

    /**
     * Check if page has widgets
     */
    public function hasWidgets(): bool
    {
        return !empty($this->getWidgetsConfig());
    }

    /**
     * Get widget count
     */
    public function getWidgetCount(): int
    {
        return count($this->getWidgetsConfig());
    }

    /**
     * Get available widget types for this page
     */
    public function getAvailableWidgetTypes(): array
    {
        return [
            'table' => [
                'name' => 'Data Table',
                'description' => 'Display tasks in a filterable table',
                'icon' => 'fas fa-table',
                'category' => 'data'
            ],
            'number_card' => [
                'name' => 'Number Card',
                'description' => 'Show single metric as a card',
                'icon' => 'fas fa-hashtag',
                'category' => 'metric'
            ],
            'progress_card' => [
                'name' => 'Progress Card',
                'description' => 'Display completion progress',
                'icon' => 'fas fa-chart-line',
                'category' => 'metric'
            ],
            'status_chart' => [
                'name' => 'Status Chart',
                'description' => 'Pie chart of task statuses',
                'icon' => 'fas fa-chart-pie',
                'category' => 'chart'
            ],
            'timeline_card' => [
                'name' => 'Timeline Card',
                'description' => 'Show upcoming deadlines',
                'icon' => 'fas fa-calendar-alt',
                'category' => 'timeline'
            ],
            'assignee_chart' => [
                'name' => 'Assignee Chart',
                'description' => 'Tasks distribution by assignee',
                'icon' => 'fas fa-users',
                'category' => 'chart'
            ]
        ];
    }
}
