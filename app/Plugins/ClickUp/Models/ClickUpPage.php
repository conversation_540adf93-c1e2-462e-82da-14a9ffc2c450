<?php

namespace App\Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ClickUpPage extends Model
{
    protected $table = 'clickup_pages';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'route',
        'icon',
        'enabled',
        'is_default',
        'sort_order',
        'layout_config',
        'filter_config',
        'widget_config',
        'page_type'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'is_default' => 'boolean',
        'layout_config' => 'array',
        'filter_config' => 'array',
        'widget_config' => 'array',
        'sort_order' => 'integer'
    ];

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug from name if not provided
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->name);
            }

            // Auto-generate route if not provided
            if (empty($page->route)) {
                $page->route = 'clickup.' . $page->slug;
            }
        });

        // Ensure only one default page
        static::saving(function ($page) {
            if ($page->is_default) {
                static::where('id', '!=', $page->id)->update(['is_default' => false]);
            }
        });
    }

    /**
     * Relationship: Page has many lists
     */
    public function lists(): HasMany
    {
        return $this->hasMany(ClickUpPageList::class, 'clickup_page_id')->orderBy('sort_order');
    }

    /**
     * Relationship: Page has many folders
     */
    public function folders(): HasMany
    {
        return $this->hasMany(ClickUpPageFolder::class, 'clickup_page_id')->orderBy('sort_order');
    }

    /**
     * Scope: Only enabled pages
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope: Order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the default page
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Get enabled lists for this page
     */
    public function getEnabledLists()
    {
        return $this->lists()->where('enabled', true)->get();
    }

    /**
     * Get enabled folders for this page
     */
    public function getEnabledFolders()
    {
        return $this->folders()->where('enabled', true)->get();
    }

    /**
     * Get the full route name for this page
     */
    public function getRouteAttribute($value)
    {
        return $value ?: 'clickup.' . $this->slug;
    }

    /**
     * Get the URL for this page
     */
    public function getUrlAttribute()
    {
        return route($this->route);
    }

    /**
     * Check if this page has any associated lists
     */
    public function hasLists()
    {
        return $this->lists()->exists();
    }

    /**
     * Check if this page has any associated folders
     */
    public function hasFolders()
    {
        return $this->folders()->exists();
    }

    /**
     * Get layout configuration with defaults
     */
    public function getLayoutConfig()
    {
        return array_merge([
            'columns' => 2,
            'show_charts' => true,
            'show_filters' => true,
            'show_stats' => true,
            'card_size' => 'medium'
        ], $this->layout_config ?? []);
    }

    /**
     * Get filter configuration with defaults
     */
    public function getFilterConfig()
    {
        return array_merge([
            'default_status' => 'all',
            'default_assignee' => 'all',
            'default_priority' => 'all',
            'show_completed' => true,
            'date_range' => 30
        ], $this->filter_config ?? []);
    }

    /**
     * Get widget configuration with defaults
     */
    public function getWidgetConfig()
    {
        return array_merge([
            'show_progress_chart' => true,
            'show_status_distribution' => true,
            'show_team_performance' => true,
            'show_overdue_alerts' => true,
            'refresh_interval' => 300 // 5 minutes
        ], $this->widget_config ?? []);
    }
}
