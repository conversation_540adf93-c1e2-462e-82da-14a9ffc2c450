<?php

use App\Plugins\ClickUp\Controllers\ClickUpController;
use App\Plugins\ClickUp\Controllers\PageBuilderController;
use Illuminate\Support\Facades\Route;

// ClickUp Plugin Web Routes

// Overview page
Route::get('/overview', [ClickUpController::class, 'overviewPage'])->name('clickup.overview');

// Page Builder routes
Route::get('/builder/{pageId}', [PageBuilderController::class, 'builder'])->name('clickup.builder');

// Dynamic pages created by users
Route::get('/pages/{slug}', [ClickUpController::class, 'dynamicPage'])->name('clickup.page');

// Settings page
Route::get('/settings', [ClickUpController::class, 'settingsPage'])->name('clickup.settings');
Route::post('/settings', [ClickUpController::class, 'saveSettings'])->name('clickup.settings.save');

// Legacy routes (keeping for backward compatibility)
Route::get('/tasks', [ClickUpController::class, 'tasksPage'])->name('clickup.tasks');
Route::get('/progress', [ClickUpController::class, 'progressPage'])->name('clickup.progress');
Route::get('/team', [ClickUpController::class, 'teamPage'])->name('clickup.team');
Route::get('/overdue', [ClickUpController::class, 'overduePage'])->name('clickup.overdue');

// Dynamic list-based pages
Route::get('/lists', [ClickUpController::class, 'listsPage'])->name('clickup.lists');
Route::get('/list/{listId}', [ClickUpController::class, 'listPage'])->name('clickup.list');

// Configuration pages
Route::get('/configure', [ClickUpController::class, 'configurePage'])->name('clickup.configure');
Route::post('/configure', [ClickUpController::class, 'saveConfiguration'])->name('clickup.configure.save');
