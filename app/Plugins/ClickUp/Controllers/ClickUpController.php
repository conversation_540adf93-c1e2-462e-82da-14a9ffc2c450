<?php

namespace App\Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\ClickUp\Services\ClickUpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ClickUp<PERSON>ontroller extends Controller
{
    protected ClickUpService $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }

    /**
     * Get overall ClickUp metrics
     */
    public function getMetrics(): JsonResponse
    {
        try {
            $metrics = $this->clickUpService->getOverallMetrics();

            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultMetrics(),
            ], 500);
        }
    }

    /**
     * Get tasks with optional filters
     */
    public function getTasks(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'assignee', 'due_date']);
            $tasks = $this->clickUpService->getTasks($filters);

            return response()->json([
                'success' => true,
                'data' => $tasks,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => ['tasks' => []],
            ], 500);
        }
    }

    /**
     * Get task progress analytics
     */
    public function getProgress(): JsonResponse
    {
        try {
            $progress = $this->clickUpService->getTaskProgressMetrics();

            return response()->json([
                'success' => true,
                'data' => $progress,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultProgressMetrics(),
            ], 500);
        }
    }

    /**
     * Get overdue tasks
     */
    public function getOverdue(): JsonResponse
    {
        try {
            $overdueTasks = $this->clickUpService->getOverdueTasks();

            return response()->json([
                'success' => true,
                'data' => [
                    'overdue_tasks' => $overdueTasks,
                    'count' => count($overdueTasks),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [
                    'overdue_tasks' => [],
                    'count' => 0,
                ],
            ], 500);
        }
    }

    /**
     * Get team productivity metrics
     */
    public function getProductivity(): JsonResponse
    {
        try {
            $productivity = $this->clickUpService->getTeamProductivityMetrics();

            return response()->json([
                'success' => true,
                'data' => $productivity,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultProductivityMetrics(),
            ], 500);
        }
    }

    /**
     * Test ClickUp API connection
     */
    public function testConnection(): JsonResponse
    {
        $result = $this->clickUpService->testConnection();

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Get tasks by status
     */
    public function getTasksByStatus(): JsonResponse
    {
        try {
            $tasksByStatus = $this->clickUpService->getTasksByStatus();

            return response()->json([
                'success' => true,
                'data' => $tasksByStatus,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [
                    'open' => [],
                    'in_progress' => [],
                    'completed' => [],
                    'overdue' => [],
                ],
            ], 500);
        }
    }

    /**
     * Default metrics for error fallback
     */
    private function getDefaultMetrics(): array
    {
        return [
            'task_progress' => $this->getDefaultProgressMetrics(),
            'team_productivity' => $this->getDefaultProductivityMetrics(),
            'overdue_count' => 0,
            'urgent_overdue' => [],
        ];
    }

    /**
     * Default progress metrics for error fallback
     */
    private function getDefaultProgressMetrics(): array
    {
        return [
            'total_tasks' => 0,
            'completed_tasks' => 0,
            'in_progress_tasks' => 0,
            'open_tasks' => 0,
            'overdue_tasks' => 0,
            'completion_rate' => 0,
            'overdue_rate' => 0,
        ];
    }

    /**
     * Default productivity metrics for error fallback
     */
    private function getDefaultProductivityMetrics(): array
    {
        return [
            'weekly_completed' => 0,
            'monthly_completed' => 0,
            'assignee_stats' => [],
            'avg_completion_rate' => 0,
        ];
    }
}
