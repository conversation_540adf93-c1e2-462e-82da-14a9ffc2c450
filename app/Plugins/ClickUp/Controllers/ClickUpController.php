<?php

namespace App\Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\ClickUp\Services\ClickUpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ClickUpController extends Controller
{
    protected ClickUpService $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
        $this->registerViewPath();
    }

    /**
     * Register the plugin's view path
     */
    private function registerViewPath(): void
    {
        $pluginViewPath = app_path('Plugins/ClickUp/views');

        if (file_exists($pluginViewPath)) {
            app('view')->getFinder()->addLocation($pluginViewPath);
            app('view')->addNamespace('clickup', $pluginViewPath);
        }
    }

    /**
     * Get overall ClickUp metrics
     */
    public function getMetrics(): JsonResponse
    {
        try {
            $metrics = $this->clickUpService->getOverallMetrics();

            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultMetrics(),
            ], 500);
        }
    }

    /**
     * Get tasks with optional filters
     */
    public function getTasks(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'assignee', 'due_date']);
            $tasks = $this->clickUpService->getTasks($filters);

            return response()->json([
                'success' => true,
                'data' => $tasks,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => ['tasks' => []],
            ], 500);
        }
    }

    /**
     * Get task progress analytics
     */
    public function getProgress(): JsonResponse
    {
        try {
            $progress = $this->clickUpService->getTaskProgressMetrics();

            return response()->json([
                'success' => true,
                'data' => $progress,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultProgressMetrics(),
            ], 500);
        }
    }

    /**
     * Get overdue tasks
     */
    public function getOverdue(): JsonResponse
    {
        try {
            $overdueTasks = $this->clickUpService->getOverdueTasks();

            return response()->json([
                'success' => true,
                'data' => [
                    'overdue_tasks' => $overdueTasks,
                    'count' => count($overdueTasks),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [
                    'overdue_tasks' => [],
                    'count' => 0,
                ],
            ], 500);
        }
    }

    /**
     * Get team productivity metrics
     */
    public function getProductivity(): JsonResponse
    {
        try {
            $productivity = $this->clickUpService->getTeamProductivityMetrics();

            return response()->json([
                'success' => true,
                'data' => $productivity,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => $this->getDefaultProductivityMetrics(),
            ], 500);
        }
    }

    /**
     * Get available ClickUp spaces
     */
    public function getSpaces(): JsonResponse
    {
        try {
            $spaces = $this->clickUpService->getSpaces();

            return response()->json($spaces, $spaces['success'] ? 200 : 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'spaces' => []
            ], 500);
        }
    }

    /**
     * Test spaces endpoint with invalid token (for testing fallback)
     */
    public function getSpacesTestFailure(): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Invalid API token. Please check your ClickUp API token.',
            'spaces' => []
        ], 500);
    }

    /**
     * Test ClickUp API connection
     */
    public function testConnection(): JsonResponse
    {
        $result = $this->clickUpService->testConnection();

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Get tasks by status
     */
    public function getTasksByStatus(): JsonResponse
    {
        try {
            $tasksByStatus = $this->clickUpService->getTasksByStatus();

            return response()->json([
                'success' => true,
                'data' => $tasksByStatus,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [
                    'open' => [],
                    'in_progress' => [],
                    'completed' => [],
                    'overdue' => [],
                ],
            ], 500);
        }
    }

    /**
     * Default metrics for error fallback
     */
    private function getDefaultMetrics(): array
    {
        return [
            'task_progress' => $this->getDefaultProgressMetrics(),
            'team_productivity' => $this->getDefaultProductivityMetrics(),
            'overdue_count' => 0,
            'urgent_overdue' => [],
        ];
    }

    /**
     * Default progress metrics for error fallback
     */
    private function getDefaultProgressMetrics(): array
    {
        return [
            'total_tasks' => 0,
            'completed_tasks' => 0,
            'in_progress_tasks' => 0,
            'open_tasks' => 0,
            'overdue_tasks' => 0,
            'completion_rate' => 0,
            'overdue_rate' => 0,
        ];
    }

    /**
     * Default productivity metrics for error fallback
     */
    private function getDefaultProductivityMetrics(): array
    {
        return [
            'weekly_completed' => 0,
            'monthly_completed' => 0,
            'assignee_stats' => [],
            'avg_completion_rate' => 0,
        ];
    }

    // ============================================================================
    // WEB PAGE METHODS
    // ============================================================================

    /**
     * Tasks page - Display all tasks with filtering and insights
     */
    public function tasksPage(Request $request): View
    {
        try {
            // Get tasks data
            $filters = $request->only(['status', 'assignee', 'due_date', 'list_id']);
            $tasks = $this->clickUpService->getTasks($filters);

            // Get additional data for insights
            $tasksByStatus = $this->clickUpService->getTasksByStatus();
            $lists = $this->clickUpService->getLists();

            return view('clickup::tasks', [
                'tasks' => $tasks['data'] ?? [],
                'tasksByStatus' => $tasksByStatus['data'] ?? [],
                'lists' => $lists['data'] ?? [],
                'filters' => $filters,
                'pageTitle' => 'Tasks',
                'pageDescription' => 'Manage and track all your ClickUp tasks'
            ]);
        } catch (\Exception $e) {
            return view('clickup::tasks', [
                'tasks' => [],
                'tasksByStatus' => [],
                'lists' => [],
                'filters' => $filters ?? [],
                'error' => $e->getMessage(),
                'pageTitle' => 'Tasks',
                'pageDescription' => 'Manage and track all your ClickUp tasks'
            ]);
        }
    }

    /**
     * Progress page - Display progress analytics and insights
     */
    public function progressPage(): View
    {
        try {
            $progress = $this->clickUpService->getTaskProgressMetrics();
            $tasksByStatus = $this->clickUpService->getTasksByStatus();
            $lists = $this->clickUpService->getLists();

            return view('clickup::progress', [
                'progress' => $progress['data'] ?? [],
                'tasksByStatus' => $tasksByStatus['data'] ?? [],
                'lists' => $lists['data'] ?? [],
                'pageTitle' => 'Progress',
                'pageDescription' => 'Track project progress and completion rates'
            ]);
        } catch (\Exception $e) {
            return view('clickup::progress', [
                'progress' => [],
                'tasksByStatus' => [],
                'lists' => [],
                'error' => $e->getMessage(),
                'pageTitle' => 'Progress',
                'pageDescription' => 'Track project progress and completion rates'
            ]);
        }
    }

    /**
     * Team page - Display team productivity and performance metrics
     */
    public function teamPage(): View
    {
        try {
            $productivity = $this->clickUpService->getTeamProductivityMetrics();
            $tasks = $this->clickUpService->getTasks([]);
            $lists = $this->clickUpService->getLists();

            return view('clickup::team', [
                'productivity' => $productivity['data'] ?? [],
                'tasks' => $tasks['data'] ?? [],
                'lists' => $lists['data'] ?? [],
                'pageTitle' => 'Team',
                'pageDescription' => 'Monitor team productivity and performance'
            ]);
        } catch (\Exception $e) {
            return view('clickup::team', [
                'productivity' => [],
                'tasks' => [],
                'lists' => [],
                'error' => $e->getMessage(),
                'pageTitle' => 'Team',
                'pageDescription' => 'Monitor team productivity and performance'
            ]);
        }
    }

    /**
     * Overdue page - Display overdue tasks and management tools
     */
    public function overduePage(): View
    {
        try {
            $overdueTasks = $this->clickUpService->getOverdueTasks();
            $lists = $this->clickUpService->getLists();

            return view('clickup::overdue', [
                'overdueTasks' => $overdueTasks['data'] ?? [],
                'lists' => $lists['data'] ?? [],
                'pageTitle' => 'Overdue Tasks',
                'pageDescription' => 'Manage and prioritize overdue tasks'
            ]);
        } catch (\Exception $e) {
            return view('clickup::overdue', [
                'overdueTasks' => [],
                'lists' => [],
                'error' => $e->getMessage(),
                'pageTitle' => 'Overdue Tasks',
                'pageDescription' => 'Manage and prioritize overdue tasks'
            ]);
        }
    }

    /**
     * Get lists from ClickUp API
     */
    public function getLists(): JsonResponse
    {
        try {
            $response = $this->clickUpService->getLists();

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch lists: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get folders from ClickUp API
     */
    public function getFolders(): JsonResponse
    {
        try {
            $response = $this->clickUpService->getFolders();

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch folders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available spaces for hierarchical selection
     */
    public function getAvailableSpaces(): JsonResponse
    {
        try {
            $response = $this->clickUpService->getAvailableSpaces();

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch available spaces: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get space hierarchy (folders with lists)
     */
    public function getSpaceHierarchy(string $spaceId): JsonResponse
    {
        try {
            $response = $this->clickUpService->getSpaceHierarchy($spaceId);

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch space hierarchy: ' . $e->getMessage()
            ], 500);
        }
    }
}
