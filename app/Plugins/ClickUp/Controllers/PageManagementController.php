<?php

namespace App\Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\ClickUp\Models\ClickUpPage;
use App\Plugins\ClickUp\Models\ClickUpPageList;
use App\Plugins\ClickUp\Models\ClickUpPageFolder;
use App\Plugins\ClickUp\Services\ClickUpService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class PageManagementController extends Controller
{
    protected ClickUpService $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }

    /**
     * Get all pages with their associations
     */
    public function index(): JsonResponse
    {
        try {
            $pages = ClickUpPage::with(['lists', 'folders'])
                ->ordered()
                ->get()
                ->map(function ($page) {
                    return [
                        'id' => $page->id,
                        'name' => $page->name,
                        'slug' => $page->slug,
                        'description' => $page->description,
                        'route' => $page->route,
                        'icon' => $page->icon,
                        'enabled' => $page->enabled,
                        'is_default' => $page->is_default,
                        'sort_order' => $page->sort_order,
                        'page_type' => $page->page_type,
                        'lists_count' => $page->lists->count(),
                        'folders_count' => $page->folders->count(),
                        'created_at' => $page->created_at,
                        'updated_at' => $page->updated_at
                    ];
                });

            return response()->json([
                'success' => true,
                'pages' => $pages,
                'count' => $pages->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch pages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new page
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'icon' => 'nullable|string|max:100',
                'page_type' => 'required|string|in:custom,tasks,progress,team,overdue',
                'enabled' => 'boolean',
                'is_default' => 'boolean',
                'layout_config' => 'nullable|array',
                'filter_config' => 'nullable|array',
                'widget_config' => 'nullable|array',
                'lists' => 'nullable|array',
                'lists.*' => 'string',
                'folders' => 'nullable|array',
                'folders.*' => 'string'
            ]);

            // Generate slug and route
            $slug = Str::slug($validated['name']);
            $route = 'clickup.' . $slug;

            // Check for duplicate slug/route
            if (ClickUpPage::where('slug', $slug)->exists()) {
                $slug .= '-' . time();
                $route = 'clickup.' . $slug;
            }

            $page = ClickUpPage::create([
                'name' => $validated['name'],
                'slug' => $slug,
                'description' => $validated['description'] ?? null,
                'route' => $route,
                'icon' => $validated['icon'] ?? 'fas fa-page',
                'enabled' => $validated['enabled'] ?? true,
                'is_default' => $validated['is_default'] ?? false,
                'sort_order' => ClickUpPage::max('sort_order') + 1,
                'page_type' => $validated['page_type'],
                'layout_config' => $validated['layout_config'] ?? null,
                'filter_config' => $validated['filter_config'] ?? null,
                'widget_config' => $validated['widget_config'] ?? null
            ]);

            // Associate lists
            if (!empty($validated['lists'])) {
                $this->associateLists($page, $validated['lists']);
            }

            // Associate folders
            if (!empty($validated['folders'])) {
                $this->associateFolders($page, $validated['folders']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Page created successfully',
                'page' => $page->load(['lists', 'folders'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing page
     */
    public function update(Request $request, $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'icon' => 'nullable|string|max:100',
                'enabled' => 'boolean',
                'is_default' => 'boolean',
                'layout_config' => 'nullable|array',
                'filter_config' => 'nullable|array',
                'widget_config' => 'nullable|array',
                'lists' => 'nullable|array',
                'lists.*' => 'string',
                'folders' => 'nullable|array',
                'folders.*' => 'string'
            ]);

            $page->update([
                'name' => $validated['name'],
                'description' => $validated['description'] ?? null,
                'icon' => $validated['icon'] ?? $page->icon,
                'enabled' => $validated['enabled'] ?? $page->enabled,
                'is_default' => $validated['is_default'] ?? $page->is_default,
                'layout_config' => $validated['layout_config'] ?? $page->layout_config,
                'filter_config' => $validated['filter_config'] ?? $page->filter_config,
                'widget_config' => $validated['widget_config'] ?? $page->widget_config
            ]);

            // Update list associations
            if (isset($validated['lists'])) {
                $page->lists()->delete();
                if (!empty($validated['lists'])) {
                    $this->associateLists($page, $validated['lists']);
                }
            }

            // Update folder associations
            if (isset($validated['folders'])) {
                $page->folders()->delete();
                if (!empty($validated['folders'])) {
                    $this->associateFolders($page, $validated['folders']);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Page updated successfully',
                'page' => $page->fresh()->load(['lists', 'folders'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a page
     */
    public function destroy($pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            $page->delete();

            return response()->json([
                'success' => true,
                'message' => 'Page deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update page order
     */
    public function updateOrder(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'pages' => 'required|array',
                'pages.*.id' => 'required|integer|exists:clickup_pages,id',
                'pages.*.sort_order' => 'required|integer'
            ]);

            foreach ($validated['pages'] as $pageData) {
                ClickUpPage::where('id', $pageData['id'])
                    ->update(['sort_order' => $pageData['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Page order updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update page order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Associate lists with a page
     */
    private function associateLists(ClickUpPage $page, array $listIds): void
    {
        $lists = $this->clickUpService->getLists();
        
        if ($lists['success']) {
            $availableLists = collect($lists['data'])->keyBy('id');
            
            foreach ($listIds as $index => $listId) {
                if ($availableLists->has($listId)) {
                    $list = $availableLists->get($listId);
                    ClickUpPageList::create([
                        'clickup_page_id' => $page->id,
                        'clickup_list_id' => $listId,
                        'list_name' => $list['name'],
                        'sort_order' => $index,
                        'enabled' => true
                    ]);
                }
            }
        }
    }

    /**
     * Associate folders with a page
     */
    private function associateFolders(ClickUpPage $page, array $folderIds): void
    {
        $folders = $this->clickUpService->getFolders();
        
        if ($folders['success']) {
            $availableFolders = collect($folders['data'])->keyBy('id');
            
            foreach ($folderIds as $index => $folderId) {
                if ($availableFolders->has($folderId)) {
                    $folder = $availableFolders->get($folderId);
                    ClickUpPageFolder::create([
                        'clickup_page_id' => $page->id,
                        'clickup_folder_id' => $folderId,
                        'folder_name' => $folder['name'],
                        'sort_order' => $index,
                        'enabled' => true
                    ]);
                }
            }
        }
    }
}
