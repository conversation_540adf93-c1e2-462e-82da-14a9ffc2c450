<?php

namespace App\Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\ClickUp\Models\ClickUpDataSource;
use App\Plugins\ClickUp\Services\DataSourceRegistry;
use App\Plugins\ClickUp\Services\ClickUpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DataSourceController extends Controller
{
    protected DataSourceRegistry $dataSourceRegistry;
    protected ClickUpService $clickUpService;

    public function __construct(DataSourceRegistry $dataSourceRegistry, ClickUpService $clickUpService)
    {
        $this->dataSourceRegistry = $dataSourceRegistry;
        $this->clickUpService = $clickUpService;
    }

    /**
     * Get all data sources
     */
    public function index(): JsonResponse
    {
        try {
            $dataSources = $this->dataSourceRegistry->getAllDataSources();

            return response()->json([
                'success' => true,
                'data' => $dataSources
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load data sources: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific data source
     */
    public function show(string $id): JsonResponse
    {
        try {
            $dataSource = $this->dataSourceRegistry->getDataSource($id);

            if (!$dataSource) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data source not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $dataSource
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load data source: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new data source
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'sources' => 'required|array|min:1',
                'sources.*.list_id' => 'required|string',
                'sources.*.list_name' => 'required|string',
                'sources.*.folder_id' => 'required|string',
                'sources.*.folder_name' => 'required|string',
                'sources.*.space_id' => 'required|string',
                'sources.*.space_name' => 'required|string',
                'configuration' => 'nullable|array',
                'enabled' => 'boolean'
            ]);

            // Validate data source configuration
            $errors = $this->dataSourceRegistry->validateDataSource($validated);
            if (!empty($errors)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 422);
            }

            $dataSource = $this->dataSourceRegistry->createDataSource($validated);

            return response()->json([
                'success' => true,
                'message' => 'Data source created successfully',
                'data' => $dataSource->toCompatibleArray()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create data source: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing data source
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string|max:500',
                'sources' => 'sometimes|required|array|min:1',
                'sources.*.list_id' => 'required_with:sources|string',
                'sources.*.list_name' => 'required_with:sources|string',
                'sources.*.folder_id' => 'required_with:sources|string',
                'sources.*.folder_name' => 'required_with:sources|string',
                'sources.*.space_id' => 'required_with:sources|string',
                'sources.*.space_name' => 'required_with:sources|string',
                'configuration' => 'nullable|array',
                'enabled' => 'boolean'
            ]);

            // Validate data source configuration if sources are being updated
            if (isset($validated['sources'])) {
                $errors = $this->dataSourceRegistry->validateDataSource($validated);
                if (!empty($errors)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $errors
                    ], 422);
                }
            }

            $dataSource = $this->dataSourceRegistry->updateDataSource($id, $validated);

            if (!$dataSource) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data source not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Data source updated successfully',
                'data' => $dataSource->toCompatibleArray()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update data source: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a data source
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $success = $this->dataSourceRegistry->deleteDataSource($id);

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data source not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Data source deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete data source: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get data from a data source
     */
    public function getData(Request $request, string $id): JsonResponse
    {
        try {
            $filters = $request->get('filters', []);
            $pagination = [
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 25)
            ];

            $result = $this->dataSourceRegistry->getData($id, $filters, $pagination);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test a data source configuration
     */
    public function test(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'sources' => 'required|array|min:1',
                'sources.*.list_id' => 'required|string',
                'sources.*.list_name' => 'required|string'
            ]);

            $testResults = [];
            $totalTasks = 0;

            foreach ($validated['sources'] as $source) {
                try {
                    $result = $this->clickUpService->getListTasks($source['list_id'], [], ['page' => 1, 'per_page' => 5]);
                    
                    $testResults[] = [
                        'list_id' => $source['list_id'],
                        'list_name' => $source['list_name'],
                        'success' => $result['success'],
                        'task_count' => $result['success'] ? $result['count'] : 0,
                        'message' => $result['success'] ? 'Connected successfully' : $result['message']
                    ];

                    if ($result['success']) {
                        $totalTasks += $result['count'];
                    }
                } catch (\Exception $e) {
                    $testResults[] = [
                        'list_id' => $source['list_id'],
                        'list_name' => $source['list_name'],
                        'success' => false,
                        'task_count' => 0,
                        'message' => 'Connection failed: ' . $e->getMessage()
                    ];
                }
            }

            $successCount = count(array_filter($testResults, fn($result) => $result['success']));
            $overallSuccess = $successCount > 0;

            return response()->json([
                'success' => $overallSuccess,
                'message' => $overallSuccess 
                    ? "Successfully connected to {$successCount} of " . count($testResults) . " sources"
                    : 'Failed to connect to any sources',
                'data' => [
                    'test_results' => $testResults,
                    'summary' => [
                        'total_sources' => count($testResults),
                        'successful_sources' => $successCount,
                        'failed_sources' => count($testResults) - $successCount,
                        'total_tasks' => $totalTasks
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test data source: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available ClickUp hierarchy for data source creation
     */
    public function getClickUpHierarchy(): JsonResponse
    {
        try {
            // Get available spaces
            $spacesResult = $this->clickUpService->getAvailableSpaces();
            
            if (!$spacesResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to load ClickUp spaces: ' . $spacesResult['message']
                ], 500);
            }

            $hierarchy = [];
            
            foreach ($spacesResult['data'] as $space) {
                try {
                    $spaceHierarchy = $this->clickUpService->getSpaceHierarchy($space['id']);
                    
                    if ($spaceHierarchy['success']) {
                        $hierarchy[] = [
                            'space' => $space,
                            'folders' => $spaceHierarchy['data']['folders'] ?? []
                        ];
                    }
                } catch (\Exception $e) {
                    // Skip spaces that can't be loaded
                    continue;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $hierarchy
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load ClickUp hierarchy: ' . $e->getMessage()
            ], 500);
        }
    }
}
