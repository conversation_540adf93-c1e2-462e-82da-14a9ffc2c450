<?php

namespace App\Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\ClickUp\Models\ClickUpPage;
use App\Plugins\ClickUp\Services\ClickUpService;
use App\Plugins\ClickUp\Services\WidgetService;
use App\Plugins\ClickUp\Services\DataSourceRegistry;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PageBuilderController extends Controller
{
    protected ClickUpService $clickUpService;
    protected WidgetService $widgetService;
    protected DataSourceRegistry $dataSourceRegistry;

    public function __construct(ClickUpService $clickUpService, WidgetService $widgetService, DataSourceRegistry $dataSourceRegistry)
    {
        $this->clickUpService = $clickUpService;
        $this->widgetService = $widgetService;
        $this->dataSourceRegistry = $dataSourceRegistry;
    }

    /**
     * Show the page builder interface
     */
    public function builder(string $pageId)
    {
        $page = ClickUpPage::findOrFail($pageId);
        
        return view('clickup::page-builder', [
            'page' => $page,
            'widgetTypes' => $page->getAvailableWidgetTypes(),
            'dataSources' => $page->getDataSources()
        ]);
    }

    /**
     * Get page builder data
     */
    public function getBuilderData(string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            
            // Get all available data sources (custom + legacy)
            $dataSources = $this->dataSourceRegistry->getAllDataSources();

            // Add legacy data sources from page for backward compatibility
            $legacyDataSources = $page->getDataSources();
            foreach ($legacyDataSources as $index => $source) {
                if (!isset($source['id'])) {
                    $source['id'] = 'legacy_' . $index;
                }
                $source['type'] = 'legacy';
                $source['name'] = $source['list_name'] ?? 'Legacy Source';
                $dataSources[] = $source;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'page' => [
                        'id' => $page->id,
                        'name' => $page->name,
                        'slug' => $page->slug,
                        'description' => $page->description,
                        'layout_type' => $page->layout_type,
                        'is_published' => $page->is_published
                    ],
                    'layout' => $page->getPageLayout(),
                    'widgets' => $page->getWidgetsConfig(),
                    'data_sources' => $dataSources,
                    'global_filters' => $page->getGlobalFilters(),
                    'widget_types' => $page->getAvailableWidgetTypes()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load page builder data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a widget to the page
     */
    public function addWidget(Request $request, string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            
            $validated = $request->validate([
                'type' => 'required|string',
                'title' => 'required|string|max:255',
                'data_source_id' => 'required|string',
                'position' => 'required|array',
                'position.x' => 'required|integer|min:0',
                'position.y' => 'required|integer|min:0',
                'position.width' => 'required|integer|min:1|max:12',
                'position.height' => 'required|integer|min:1',
                'config' => 'nullable|array'
            ]);

            $widgetConfig = [
                'id' => uniqid('widget_'),
                'type' => $validated['type'],
                'title' => $validated['title'],
                'data_source_id' => $validated['data_source_id'],
                'position' => $validated['position'],
                'config' => $validated['config'] ?? [],
                'created_at' => now()->toISOString()
            ];

            $page->addWidget($widgetConfig);

            return response()->json([
                'success' => true,
                'message' => 'Widget added successfully',
                'widget' => $widgetConfig
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a widget configuration
     */
    public function updateWidget(Request $request, string $pageId, string $widgetId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            
            $validated = $request->validate([
                'title' => 'sometimes|string|max:255',
                'position' => 'sometimes|array',
                'config' => 'sometimes|array'
            ]);

            $success = $page->updateWidget($widgetId, $validated);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Widget updated successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Widget not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove a widget from the page
     */
    public function removeWidget(string $pageId, string $widgetId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            
            $success = $page->removeWidget($widgetId);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Widget removed successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Widget not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update page layout
     */
    public function updateLayout(Request $request, string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            
            $validated = $request->validate([
                'layout' => 'required|array',
                'layout_type' => 'sometimes|string|in:grid,flex,custom'
            ]);

            $updateData = ['page_layout' => $validated['layout']];
            
            if (isset($validated['layout_type'])) {
                $updateData['layout_type'] = $validated['layout_type'];
            }

            $page->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Layout updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update layout: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Render widget data
     */
    public function renderWidget(Request $request, string $pageId, string $widgetId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            $widgets = $page->getWidgetsConfig();
            
            $widget = collect($widgets)->firstWhere('id', $widgetId);
            
            if (!$widget) {
                return response()->json([
                    'success' => false,
                    'message' => 'Widget not found'
                ], 404);
            }

            $renderedWidget = $this->widgetService->renderWidget($widget, $page);

            return response()->json([
                'success' => true,
                'data' => $renderedWidget
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to render widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Preview page with current configuration
     */
    public function preview(string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            $widgets = $page->getWidgetsConfig();
            $renderedWidgets = [];

            foreach ($widgets as $widget) {
                $renderedWidgets[] = $this->widgetService->renderWidget($widget, $page);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'page' => [
                        'id' => $page->id,
                        'name' => $page->name,
                        'description' => $page->description,
                        'layout' => $page->getPageLayout()
                    ],
                    'widgets' => $renderedWidgets
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Publish the page
     */
    public function publish(string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            $page->publish();

            return response()->json([
                'success' => true,
                'message' => 'Page published successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to publish page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unpublish the page
     */
    public function unpublish(string $pageId): JsonResponse
    {
        try {
            $page = ClickUpPage::findOrFail($pageId);
            $page->unpublish();

            return response()->json([
                'success' => true,
                'message' => 'Page unpublished successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unpublish page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available data sources for widget configuration
     */
    public function getDataSources(string $pageId): JsonResponse
    {
        try {
            // Get all available data sources from registry
            $dataSources = $this->dataSourceRegistry->getAllDataSources();

            return response()->json([
                'success' => true,
                'data' => $dataSources
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load data sources: ' . $e->getMessage()
            ], 500);
        }
    }
}
