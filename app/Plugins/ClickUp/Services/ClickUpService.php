<?php

namespace App\Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ClickUpService
{
    protected string $baseUrl = 'https://api.clickup.com/api/v2';
    protected ?string $apiToken;
    protected ?string $spaceId;
    protected ?string $teamId;
    protected int $cacheMinutes = 5; // Cache API responses for 5 minutes

    public function __construct()
    {
        $pluginManager = app(\App\Services\PluginManager::class);
        $config = $pluginManager->getPluginConfig('ClickUp');

        $this->apiToken = $config['api_token'] ?? null;
        $this->spaceId = $config['space_id'] ?? null;
        $this->teamId = $config['team_id'] ?? null;
    }

    /**
     * Make authenticated request to ClickUp API
     */
    protected function makeRequest(string $endpoint, array $params = []): array
    {
        if (!$this->apiToken) {
            throw new \Exception('ClickUp API token not configured');
        }

        $cacheKey = 'clickup_' . md5($endpoint . serialize($params));
        
        return Cache::remember($cacheKey, $this->cacheMinutes * 60, function () use ($endpoint, $params) {
            try {
                $response = Http::withHeaders([
                    'Authorization' => $this->apiToken,
                    'Content-Type' => 'application/json',
                ])->get($this->baseUrl . $endpoint, $params);

                if ($response->failed()) {
                    Log::error('ClickUp API request failed', [
                        'endpoint' => $endpoint,
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);
                    
                    throw new \Exception('ClickUp API request failed: ' . $response->status());
                }

                return $response->json();
            } catch (\Exception $e) {
                Log::error('ClickUp API error', ['error' => $e->getMessage()]);
                throw $e;
            }
        });
    }

    /**
     * Get all tasks from the configured space
     */
    public function getTasks(array $filters = []): array
    {
        if (!$this->spaceId) {
            throw new \Exception('ClickUp Space ID not configured');
        }

        $params = array_merge([
            'archived' => false,
            'include_closed' => true,
        ], $filters);

        return $this->makeRequest("/space/{$this->spaceId}/task", $params);
    }

    /**
     * Get tasks by status
     */
    public function getTasksByStatus(): array
    {
        $tasks = $this->getTasks();
        $tasksByStatus = [
            'open' => [],
            'in_progress' => [],
            'completed' => [],
            'overdue' => []
        ];

        foreach ($tasks['tasks'] ?? [] as $task) {
            $status = strtolower($task['status']['status'] ?? 'open');
            $dueDate = $task['due_date'] ?? null;
            
            // Check if task is overdue
            if ($dueDate && Carbon::createFromTimestamp($dueDate / 1000)->isPast() && $status !== 'complete') {
                $tasksByStatus['overdue'][] = $task;
            } else {
                // Map ClickUp statuses to our categories
                switch ($status) {
                    case 'complete':
                    case 'closed':
                        $tasksByStatus['completed'][] = $task;
                        break;
                    case 'in progress':
                    case 'in review':
                        $tasksByStatus['in_progress'][] = $task;
                        break;
                    default:
                        $tasksByStatus['open'][] = $task;
                }
            }
        }

        return $tasksByStatus;
    }

    /**
     * Get task progress metrics
     */
    public function getTaskProgressMetrics(): array
    {
        $tasksByStatus = $this->getTasksByStatus();
        
        $totalTasks = array_sum(array_map('count', $tasksByStatus));
        $completedTasks = count($tasksByStatus['completed']);
        $inProgressTasks = count($tasksByStatus['in_progress']);
        $openTasks = count($tasksByStatus['open']);
        $overdueTasks = count($tasksByStatus['overdue']);

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'in_progress_tasks' => $inProgressTasks,
            'open_tasks' => $openTasks,
            'overdue_tasks' => $overdueTasks,
            'completion_rate' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0,
            'overdue_rate' => $totalTasks > 0 ? round(($overdueTasks / $totalTasks) * 100, 2) : 0,
        ];
    }

    /**
     * Get overdue tasks
     */
    public function getOverdueTasks(): array
    {
        $tasksByStatus = $this->getTasksByStatus();
        return $tasksByStatus['overdue'];
    }

    /**
     * Get team productivity insights
     */
    public function getTeamProductivityMetrics(): array
    {
        $tasks = $this->getTasks();
        $now = Carbon::now();
        $weekAgo = $now->copy()->subWeek();
        $monthAgo = $now->copy()->subMonth();

        $weeklyCompleted = 0;
        $monthlyCompleted = 0;
        $assigneeStats = [];

        foreach ($tasks['tasks'] ?? [] as $task) {
            $status = strtolower($task['status']['status'] ?? '');
            $dateUpdated = isset($task['date_updated']) ? Carbon::createFromTimestamp($task['date_updated'] / 1000) : null;
            
            if ($status === 'complete' && $dateUpdated) {
                if ($dateUpdated->greaterThan($weekAgo)) {
                    $weeklyCompleted++;
                }
                if ($dateUpdated->greaterThan($monthAgo)) {
                    $monthlyCompleted++;
                }
            }

            // Track assignee statistics
            foreach ($task['assignees'] ?? [] as $assignee) {
                $userId = $assignee['id'];
                $userName = $assignee['username'] ?? 'Unknown';
                
                if (!isset($assigneeStats[$userId])) {
                    $assigneeStats[$userId] = [
                        'name' => $userName,
                        'total_tasks' => 0,
                        'completed_tasks' => 0,
                    ];
                }
                
                $assigneeStats[$userId]['total_tasks']++;
                if ($status === 'complete') {
                    $assigneeStats[$userId]['completed_tasks']++;
                }
            }
        }

        // Calculate completion rates for assignees
        foreach ($assigneeStats as &$stats) {
            $stats['completion_rate'] = $stats['total_tasks'] > 0 
                ? round(($stats['completed_tasks'] / $stats['total_tasks']) * 100, 2) 
                : 0;
        }

        return [
            'weekly_completed' => $weeklyCompleted,
            'monthly_completed' => $monthlyCompleted,
            'assignee_stats' => array_values($assigneeStats),
            'avg_completion_rate' => count($assigneeStats) > 0 
                ? round(array_sum(array_column($assigneeStats, 'completion_rate')) / count($assigneeStats), 2)
                : 0,
        ];
    }

    /**
     * Get overall metrics for dashboard
     */
    public function getOverallMetrics(): array
    {
        $progressMetrics = $this->getTaskProgressMetrics();
        $productivityMetrics = $this->getTeamProductivityMetrics();
        $overdueTasks = $this->getOverdueTasks();

        return [
            'task_progress' => $progressMetrics,
            'team_productivity' => $productivityMetrics,
            'overdue_count' => count($overdueTasks),
            'urgent_overdue' => array_slice($overdueTasks, 0, 5), // Top 5 overdue tasks
        ];
    }

    /**
     * Get lists from the configured space
     */
    public function getLists(): array
    {
        if (!$this->spaceId) {
            return [
                'success' => false,
                'message' => 'ClickUp Space ID not configured',
                'data' => []
            ];
        }

        try {
            $response = $this->makeRequest("/space/{$this->spaceId}/list");

            if (isset($response['lists'])) {
                return [
                    'success' => true,
                    'data' => $response['lists'],
                    'count' => count($response['lists'])
                ];
            }

            return [
                'success' => false,
                'message' => 'No lists found in the configured space',
                'data' => []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch lists: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get folders from the configured space
     */
    public function getFolders(): array
    {
        if (!$this->spaceId) {
            return [
                'success' => false,
                'message' => 'ClickUp Space ID not configured',
                'data' => []
            ];
        }

        try {
            $response = $this->makeRequest("/space/{$this->spaceId}/folder");

            if (isset($response['folders'])) {
                return [
                    'success' => true,
                    'data' => $response['folders'],
                    'count' => count($response['folders'])
                ];
            }

            return [
                'success' => false,
                'message' => 'No folders found in the configured space',
                'data' => []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch folders: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get lists from a specific folder
     */
    public function getFolderLists(string $folderId): array
    {
        try {
            $response = $this->makeRequest("/folder/{$folderId}/list");

            if (isset($response['lists'])) {
                return [
                    'success' => true,
                    'data' => $response['lists'],
                    'count' => count($response['lists'])
                ];
            }

            return [
                'success' => false,
                'message' => 'No lists found in the specified folder',
                'data' => []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch folder lists: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get hierarchical data for a specific space (folders with their lists)
     */
    public function getSpaceHierarchy(string $spaceId): array
    {
        try {
            // Get folders for the space
            $foldersResponse = $this->makeRequest("/space/{$spaceId}/folder");

            if (!isset($foldersResponse['folders'])) {
                return [
                    'success' => false,
                    'message' => 'No folders found in the specified space',
                    'data' => []
                ];
            }

            $hierarchy = [];

            foreach ($foldersResponse['folders'] as $folder) {
                $folderData = [
                    'id' => $folder['id'],
                    'name' => $folder['name'],
                    'lists' => []
                ];

                // Get lists for this folder
                try {
                    $listsResponse = $this->makeRequest("/folder/{$folder['id']}/list");
                    if (isset($listsResponse['lists'])) {
                        $folderData['lists'] = array_map(function($list) {
                            return [
                                'id' => $list['id'],
                                'name' => $list['name']
                            ];
                        }, $listsResponse['lists']);
                    }
                } catch (\Exception $e) {
                    // Continue if we can't get lists for this folder
                    \Log::warning("Failed to get lists for folder {$folder['id']}: " . $e->getMessage());
                }

                $hierarchy[] = $folderData;
            }

            return [
                'success' => true,
                'data' => $hierarchy,
                'count' => count($hierarchy)
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch space hierarchy: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get all available spaces for hierarchical selection
     */
    public function getAvailableSpaces(): array
    {
        if (!$this->apiToken) {
            return [
                'success' => false,
                'message' => 'ClickUp API token not configured. Please configure your API token first.',
                'data' => []
            ];
        }

        try {
            // First get the user's teams
            $teamsResponse = $this->makeRequest('/team');
            $teams = $teamsResponse['teams'] ?? [];

            if (empty($teams)) {
                return [
                    'success' => false,
                    'message' => 'No teams found. Please check your API token permissions.',
                    'data' => []
                ];
            }

            $allSpaces = [];
            $failedTeams = 0;

            foreach ($teams as $team) {
                try {
                    // Get spaces for each team
                    $spacesResponse = $this->makeRequest("/team/{$team['id']}/space");
                    $spaces = $spacesResponse['spaces'] ?? [];

                    foreach ($spaces as $space) {
                        $allSpaces[] = [
                            'id' => (string) $space['id'],
                            'name' => $space['name'],
                            'team_id' => (string) $team['id'],
                            'team_name' => $team['name']
                        ];
                    }
                } catch (\Exception $e) {
                    $failedTeams++;
                    // Log error but continue with other teams
                    \Log::warning('Failed to fetch spaces for team in getAvailableSpaces', [
                        'team_id' => $team['id'],
                        'team_name' => $team['name'] ?? 'Unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Sort spaces by team name, then by space name
            usort($allSpaces, function ($a, $b) {
                $teamCompare = strcmp($a['team_name'], $b['team_name']);
                if ($teamCompare === 0) {
                    return strcmp($a['name'], $b['name']);
                }
                return $teamCompare;
            });

            return [
                'success' => true,
                'data' => $allSpaces,
                'count' => count($allSpaces),
                'message' => $failedTeams > 0 ? "Loaded spaces (failed to load from {$failedTeams} team(s))" : 'Successfully loaded spaces'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch available spaces: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get available spaces for the user
     */
    public function getSpaces(): array
    {
        if (!$this->apiToken) {
            return [
                'success' => false,
                'message' => 'ClickUp API token not configured. Please configure your API token first.',
                'spaces' => []
            ];
        }

        try {
            // First get the user's teams
            $teamsResponse = $this->makeRequest('/team');
            $teams = $teamsResponse['teams'] ?? [];

            if (empty($teams)) {
                return [
                    'success' => false,
                    'message' => 'No teams found. Please check your API token permissions.',
                    'spaces' => []
                ];
            }

            $allSpaces = [];
            $failedTeams = 0;

            foreach ($teams as $team) {
                try {
                    // Get spaces for each team
                    $spacesResponse = $this->makeRequest("/team/{$team['id']}/space");
                    $spaces = $spacesResponse['spaces'] ?? [];

                    foreach ($spaces as $space) {
                        $allSpaces[] = [
                            'id' => (string) $space['id'],
                            'name' => $space['name'],
                            'team_id' => (string) $team['id'],
                            'team_name' => $team['name'],
                            'color' => $space['color'] ?? null,
                            'private' => $space['private'] ?? false,
                            'status' => $space['status'] ?? null,
                        ];
                    }
                } catch (\Exception $e) {
                    $failedTeams++;
                    // Log error but continue with other teams
                    Log::warning('Failed to fetch spaces for team', [
                        'team_id' => $team['id'],
                        'team_name' => $team['name'] ?? 'Unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Sort spaces by team name, then by space name
            usort($allSpaces, function ($a, $b) {
                $teamCompare = strcmp($a['team_name'], $b['team_name']);
                if ($teamCompare === 0) {
                    return strcmp($a['name'], $b['name']);
                }
                return $teamCompare;
            });

            $message = 'Successfully loaded spaces';
            if ($failedTeams > 0) {
                $message .= " (failed to load spaces from {$failedTeams} team(s))";
            }

            return [
                'success' => true,
                'spaces' => $allSpaces,
                'count' => count($allSpaces),
                'message' => $message,
                'failed_teams' => $failedTeams
            ];

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();

            // Provide more specific error messages based on the error
            if (strpos($errorMessage, '401') !== false) {
                $errorMessage = 'Invalid API token. Please check your ClickUp API token.';
            } elseif (strpos($errorMessage, '403') !== false) {
                $errorMessage = 'Access denied. Please check your API token permissions.';
            } elseif (strpos($errorMessage, '429') !== false) {
                $errorMessage = 'Rate limit exceeded. Please try again later.';
            } elseif (strpos($errorMessage, 'timeout') !== false) {
                $errorMessage = 'Request timeout. Please check your internet connection.';
            }

            Log::error('Failed to fetch ClickUp spaces', [
                'error' => $e->getMessage(),
                'api_token_configured' => !empty($this->apiToken)
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'spaces' => []
            ];
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        try {
            $response = $this->makeRequest('/user');
            return [
                'success' => true,
                'user' => $response['user'] ?? null,
                'message' => 'Successfully connected to ClickUp API'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get tasks from a specific list with advanced filtering and pagination
     */
    public function getListTasks(string $listId, array $filters = [], array $pagination = []): array
    {
        if (!$this->apiToken) {
            return [
                'success' => false,
                'message' => 'ClickUp API token not configured',
                'data' => []
            ];
        }

        try {
            // Build query parameters
            $queryParams = $this->buildTaskQueryParams($filters, $pagination);
            $queryString = http_build_query($queryParams);
            $endpoint = "/list/{$listId}/task" . ($queryString ? "?{$queryString}" : '');

            $response = $this->makeRequest($endpoint);

            if (isset($response['tasks'])) {
                $tasks = $this->processTasksData($response['tasks'], $filters);

                return [
                    'success' => true,
                    'data' => $tasks,
                    'count' => count($tasks),
                    'pagination' => [
                        'page' => $pagination['page'] ?? 1,
                        'per_page' => $pagination['per_page'] ?? 25,
                        'total' => count($tasks),
                        'has_more' => count($tasks) >= ($pagination['per_page'] ?? 25)
                    ],
                    'filters_applied' => $filters
                ];
            }

            return [
                'success' => false,
                'message' => 'No tasks found in the specified list',
                'data' => []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch list tasks: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Build query parameters for task filtering
     */
    protected function buildTaskQueryParams(array $filters, array $pagination): array
    {
        $params = [];

        // Pagination
        if (isset($pagination['page']) && $pagination['page'] > 1) {
            $params['page'] = $pagination['page'];
        }

        if (isset($pagination['per_page'])) {
            $params['limit'] = min($pagination['per_page'], 100); // ClickUp API limit
        }

        // Status filters
        if (!empty($filters['status_filter'])) {
            $params['statuses'] = $filters['status_filter'];
        }

        // Assignee filters
        if (!empty($filters['assignee_filter'])) {
            $params['assignees'] = $filters['assignee_filter'];
        }

        // Date range filters
        if (!empty($filters['date_range'])) {
            if (isset($filters['date_range']['start'])) {
                $params['date_created_gt'] = strtotime($filters['date_range']['start']) * 1000;
            }
            if (isset($filters['date_range']['end'])) {
                $params['date_created_lt'] = strtotime($filters['date_range']['end']) * 1000;
            }
        }

        // Due date filters
        if (!empty($filters['due_date_filter'])) {
            if (isset($filters['due_date_filter']['start'])) {
                $params['due_date_gt'] = strtotime($filters['due_date_filter']['start']) * 1000;
            }
            if (isset($filters['due_date_filter']['end'])) {
                $params['due_date_lt'] = strtotime($filters['due_date_filter']['end']) * 1000;
            }
        }

        // Priority filters
        if (!empty($filters['priority_filter'])) {
            $params['priority'] = $filters['priority_filter'];
        }

        // Include additional fields
        $params['include_closed'] = true;
        $params['include_markdown_description'] = false;

        return $params;
    }

    /**
     * Process and enhance tasks data
     */
    protected function processTasksData(array $tasks, array $filters): array
    {
        $processedTasks = [];

        foreach ($tasks as $task) {
            $processedTask = [
                'id' => $task['id'],
                'name' => $task['name'],
                'description' => $task['description'] ?? '',
                'status' => [
                    'status' => $task['status']['status'] ?? 'unknown',
                    'color' => $task['status']['color'] ?? '#808080',
                    'type' => $task['status']['type'] ?? 'open'
                ],
                'priority' => [
                    'priority' => $task['priority']['priority'] ?? null,
                    'color' => $task['priority']['color'] ?? null
                ],
                'assignees' => array_map(function($assignee) {
                    return [
                        'id' => $assignee['id'],
                        'username' => $assignee['username'],
                        'email' => $assignee['email'] ?? '',
                        'profilePicture' => $assignee['profilePicture'] ?? null
                    ];
                }, $task['assignees'] ?? []),
                'creator' => [
                    'id' => $task['creator']['id'] ?? null,
                    'username' => $task['creator']['username'] ?? 'Unknown'
                ],
                'watchers' => $task['watchers'] ?? [],
                'checklists' => $task['checklists'] ?? [],
                'tags' => $task['tags'] ?? [],
                'parent' => $task['parent'] ?? null,
                'date_created' => $task['date_created'] ?? null,
                'date_updated' => $task['date_updated'] ?? null,
                'date_closed' => $task['date_closed'] ?? null,
                'due_date' => $task['due_date'] ?? null,
                'start_date' => $task['start_date'] ?? null,
                'time_estimate' => $task['time_estimate'] ?? null,
                'time_spent' => $task['time_spent'] ?? null,
                'custom_fields' => $task['custom_fields'] ?? [],
                'url' => $task['url'] ?? null,
                'archived' => $task['archived'] ?? false
            ];

            // Apply client-side filters if needed
            if ($this->passesClientFilters($processedTask, $filters)) {
                $processedTasks[] = $processedTask;
            }
        }

        // Apply sorting if specified
        if (!empty($filters['sort_by'])) {
            $processedTasks = $this->sortTasks($processedTasks, $filters['sort_by'], $filters['sort_direction'] ?? 'asc');
        }

        return $processedTasks;
    }

    /**
     * Check if task passes client-side filters
     */
    protected function passesClientFilters(array $task, array $filters): bool
    {
        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = strtolower($filters['search']);
            $taskName = strtolower($task['name']);
            $taskDescription = strtolower($task['description']);

            if (strpos($taskName, $searchTerm) === false && strpos($taskDescription, $searchTerm) === false) {
                return false;
            }
        }

        // Custom field filters
        if (!empty($filters['custom_filters'])) {
            foreach ($filters['custom_filters'] as $fieldId => $value) {
                $customField = collect($task['custom_fields'])->firstWhere('id', $fieldId);
                if (!$customField || $customField['value'] !== $value) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Sort tasks by specified field
     */
    protected function sortTasks(array $tasks, string $sortBy, string $direction = 'asc'): array
    {
        usort($tasks, function($a, $b) use ($sortBy, $direction) {
            $valueA = $this->getTaskSortValue($a, $sortBy);
            $valueB = $this->getTaskSortValue($b, $sortBy);

            if ($valueA === $valueB) {
                return 0;
            }

            $result = $valueA < $valueB ? -1 : 1;
            return $direction === 'desc' ? -$result : $result;
        });

        return $tasks;
    }

    /**
     * Get sort value for a task field
     */
    protected function getTaskSortValue(array $task, string $field)
    {
        switch ($field) {
            case 'name':
                return strtolower($task['name']);
            case 'status':
                return strtolower($task['status']['status']);
            case 'priority':
                return $task['priority']['priority'] ?? 0;
            case 'due_date':
                return $task['due_date'] ? strtotime($task['due_date']) : PHP_INT_MAX;
            case 'date_created':
                return $task['date_created'] ? strtotime($task['date_created']) : 0;
            case 'date_updated':
                return $task['date_updated'] ? strtotime($task['date_updated']) : 0;
            case 'assignee':
                return !empty($task['assignees']) ? strtolower($task['assignees'][0]['username']) : 'zzz';
            default:
                return '';
        }
    }
}
