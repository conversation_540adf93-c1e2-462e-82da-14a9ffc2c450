<?php

namespace App\Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ClickUpService
{
    protected string $baseUrl = 'https://api.clickup.com/api/v2';
    protected ?string $apiToken;
    protected ?string $spaceId;
    protected ?string $teamId;
    protected int $cacheMinutes = 5; // Cache API responses for 5 minutes

    public function __construct()
    {
        $config = config('plugins.clickup', []);
        $this->apiToken = $config['api_token'] ?? null;
        $this->spaceId = $config['space_id'] ?? null;
        $this->teamId = $config['team_id'] ?? null;
    }

    /**
     * Make authenticated request to ClickUp API
     */
    protected function makeRequest(string $endpoint, array $params = []): array
    {
        if (!$this->apiToken) {
            throw new \Exception('ClickUp API token not configured');
        }

        $cacheKey = 'clickup_' . md5($endpoint . serialize($params));
        
        return Cache::remember($cacheKey, $this->cacheMinutes * 60, function () use ($endpoint, $params) {
            try {
                $response = Http::withHeaders([
                    'Authorization' => $this->apiToken,
                    'Content-Type' => 'application/json',
                ])->get($this->baseUrl . $endpoint, $params);

                if ($response->failed()) {
                    Log::error('ClickUp API request failed', [
                        'endpoint' => $endpoint,
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);
                    
                    throw new \Exception('ClickUp API request failed: ' . $response->status());
                }

                return $response->json();
            } catch (\Exception $e) {
                Log::error('ClickUp API error', ['error' => $e->getMessage()]);
                throw $e;
            }
        });
    }

    /**
     * Get all tasks from the configured space
     */
    public function getTasks(array $filters = []): array
    {
        if (!$this->spaceId) {
            throw new \Exception('ClickUp Space ID not configured');
        }

        $params = array_merge([
            'archived' => false,
            'include_closed' => true,
        ], $filters);

        return $this->makeRequest("/space/{$this->spaceId}/task", $params);
    }

    /**
     * Get tasks by status
     */
    public function getTasksByStatus(): array
    {
        $tasks = $this->getTasks();
        $tasksByStatus = [
            'open' => [],
            'in_progress' => [],
            'completed' => [],
            'overdue' => []
        ];

        foreach ($tasks['tasks'] ?? [] as $task) {
            $status = strtolower($task['status']['status'] ?? 'open');
            $dueDate = $task['due_date'] ?? null;
            
            // Check if task is overdue
            if ($dueDate && Carbon::createFromTimestamp($dueDate / 1000)->isPast() && $status !== 'complete') {
                $tasksByStatus['overdue'][] = $task;
            } else {
                // Map ClickUp statuses to our categories
                switch ($status) {
                    case 'complete':
                    case 'closed':
                        $tasksByStatus['completed'][] = $task;
                        break;
                    case 'in progress':
                    case 'in review':
                        $tasksByStatus['in_progress'][] = $task;
                        break;
                    default:
                        $tasksByStatus['open'][] = $task;
                }
            }
        }

        return $tasksByStatus;
    }

    /**
     * Get task progress metrics
     */
    public function getTaskProgressMetrics(): array
    {
        $tasksByStatus = $this->getTasksByStatus();
        
        $totalTasks = array_sum(array_map('count', $tasksByStatus));
        $completedTasks = count($tasksByStatus['completed']);
        $inProgressTasks = count($tasksByStatus['in_progress']);
        $openTasks = count($tasksByStatus['open']);
        $overdueTasks = count($tasksByStatus['overdue']);

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'in_progress_tasks' => $inProgressTasks,
            'open_tasks' => $openTasks,
            'overdue_tasks' => $overdueTasks,
            'completion_rate' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0,
            'overdue_rate' => $totalTasks > 0 ? round(($overdueTasks / $totalTasks) * 100, 2) : 0,
        ];
    }

    /**
     * Get overdue tasks
     */
    public function getOverdueTasks(): array
    {
        $tasksByStatus = $this->getTasksByStatus();
        return $tasksByStatus['overdue'];
    }

    /**
     * Get team productivity insights
     */
    public function getTeamProductivityMetrics(): array
    {
        $tasks = $this->getTasks();
        $now = Carbon::now();
        $weekAgo = $now->copy()->subWeek();
        $monthAgo = $now->copy()->subMonth();

        $weeklyCompleted = 0;
        $monthlyCompleted = 0;
        $assigneeStats = [];

        foreach ($tasks['tasks'] ?? [] as $task) {
            $status = strtolower($task['status']['status'] ?? '');
            $dateUpdated = isset($task['date_updated']) ? Carbon::createFromTimestamp($task['date_updated'] / 1000) : null;
            
            if ($status === 'complete' && $dateUpdated) {
                if ($dateUpdated->greaterThan($weekAgo)) {
                    $weeklyCompleted++;
                }
                if ($dateUpdated->greaterThan($monthAgo)) {
                    $monthlyCompleted++;
                }
            }

            // Track assignee statistics
            foreach ($task['assignees'] ?? [] as $assignee) {
                $userId = $assignee['id'];
                $userName = $assignee['username'] ?? 'Unknown';
                
                if (!isset($assigneeStats[$userId])) {
                    $assigneeStats[$userId] = [
                        'name' => $userName,
                        'total_tasks' => 0,
                        'completed_tasks' => 0,
                    ];
                }
                
                $assigneeStats[$userId]['total_tasks']++;
                if ($status === 'complete') {
                    $assigneeStats[$userId]['completed_tasks']++;
                }
            }
        }

        // Calculate completion rates for assignees
        foreach ($assigneeStats as &$stats) {
            $stats['completion_rate'] = $stats['total_tasks'] > 0 
                ? round(($stats['completed_tasks'] / $stats['total_tasks']) * 100, 2) 
                : 0;
        }

        return [
            'weekly_completed' => $weeklyCompleted,
            'monthly_completed' => $monthlyCompleted,
            'assignee_stats' => array_values($assigneeStats),
            'avg_completion_rate' => count($assigneeStats) > 0 
                ? round(array_sum(array_column($assigneeStats, 'completion_rate')) / count($assigneeStats), 2)
                : 0,
        ];
    }

    /**
     * Get overall metrics for dashboard
     */
    public function getOverallMetrics(): array
    {
        $progressMetrics = $this->getTaskProgressMetrics();
        $productivityMetrics = $this->getTeamProductivityMetrics();
        $overdueTasks = $this->getOverdueTasks();

        return [
            'task_progress' => $progressMetrics,
            'team_productivity' => $productivityMetrics,
            'overdue_count' => count($overdueTasks),
            'urgent_overdue' => array_slice($overdueTasks, 0, 5), // Top 5 overdue tasks
        ];
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        try {
            $response = $this->makeRequest('/user');
            return [
                'success' => true,
                'user' => $response['user'] ?? null,
                'message' => 'Successfully connected to ClickUp API'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
