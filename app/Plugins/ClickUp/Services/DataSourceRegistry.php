<?php

namespace App\Plugins\ClickUp\Services;

use App\Plugins\ClickUp\Models\ClickUpDataSource;

class DataSourceRegistry
{
    protected ClickUpService $clickUpService;
    protected array $registeredTypes = [];

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
        $this->registerDefaultTypes();
    }

    /**
     * Register default data source types
     */
    protected function registerDefaultTypes(): void
    {
        $this->registerType('clickup_list', [
            'name' => 'ClickUp List',
            'description' => 'Individual ClickUp list',
            'handler' => [$this, 'handleClickUpList'],
            'validator' => [$this, 'validateClickUpList'],
            'merger' => [$this, 'mergeClickUpLists']
        ]);

        $this->registerType('clickup_composite', [
            'name' => 'ClickUp Composite',
            'description' => 'Multiple ClickUp lists combined',
            'handler' => [$this, 'handleClickUpComposite'],
            'validator' => [$this, 'validateClickUpComposite'],
            'merger' => [$this, 'mergeClickUpLists']
        ]);
    }

    /**
     * Register a new data source type
     */
    public function registerType(string $type, array $config): void
    {
        $this->registeredTypes[$type] = array_merge([
            'name' => $type,
            'description' => '',
            'handler' => null,
            'validator' => null,
            'merger' => null
        ], $config);
    }

    /**
     * Get all registered types
     */
    public function getRegisteredTypes(): array
    {
        return $this->registeredTypes;
    }

    /**
     * Get all available data sources (custom + legacy)
     */
    public function getAllDataSources(): array
    {
        $dataSources = [];

        // Get custom data sources
        $customSources = ClickUpDataSource::getEnabled();
        foreach ($customSources as $source) {
            $dataSources[] = $source->toCompatibleArray();
        }

        return $dataSources;
    }

    /**
     * Get data from a data source
     */
    public function getData(string $dataSourceId, array $filters = [], array $pagination = []): array
    {
        // Check if it's a custom data source
        if (str_starts_with($dataSourceId, 'custom_')) {
            $id = str_replace('custom_', '', $dataSourceId);
            $dataSource = ClickUpDataSource::find($id);
            
            if (!$dataSource || !$dataSource->enabled) {
                return [
                    'success' => false,
                    'message' => 'Data source not found or disabled',
                    'data' => []
                ];
            }

            return $this->getCustomDataSourceData($dataSource, $filters, $pagination);
        }

        // Handle legacy data sources (for backward compatibility)
        return $this->getLegacyDataSourceData($dataSourceId, $filters, $pagination);
    }

    /**
     * Get data from custom data source
     */
    protected function getCustomDataSourceData(ClickUpDataSource $dataSource, array $filters, array $pagination): array
    {
        $sources = $dataSource->getIndividualSources();
        
        if (empty($sources)) {
            return [
                'success' => false,
                'message' => 'No sources configured for this data source',
                'data' => []
            ];
        }

        if ($dataSource->isSingle()) {
            // Single source - direct API call
            $source = $sources[0];
            return $this->clickUpService->getListTasks($source['list_id'], $filters, $pagination);
        }

        // Composite source - merge data from multiple sources
        return $this->mergeMultipleSourceData($sources, $filters, $pagination);
    }

    /**
     * Merge data from multiple ClickUp sources
     */
    protected function mergeMultipleSourceData(array $sources, array $filters, array $pagination): array
    {
        $allTasks = [];
        $totalCount = 0;
        $errors = [];

        foreach ($sources as $source) {
            try {
                $result = $this->clickUpService->getListTasks($source['list_id'], $filters, [
                    'page' => 1,
                    'per_page' => 100 // Get more tasks for merging
                ]);

                if ($result['success']) {
                    // Add source information to each task
                    $tasks = array_map(function($task) use ($source) {
                        $task['source'] = [
                            'list_id' => $source['list_id'],
                            'list_name' => $source['list_name'],
                            'folder_name' => $source['folder_name'],
                            'space_name' => $source['space_name']
                        ];
                        return $task;
                    }, $result['data']);

                    $allTasks = array_merge($allTasks, $tasks);
                    $totalCount += $result['count'];
                } else {
                    $errors[] = "Failed to fetch from {$source['list_name']}: {$result['message']}";
                }
            } catch (\Exception $e) {
                $errors[] = "Error fetching from {$source['list_name']}: {$e->getMessage()}";
            }
        }

        // Sort merged tasks by creation date (newest first)
        usort($allTasks, function($a, $b) {
            $dateA = $a['date_created'] ?? 0;
            $dateB = $b['date_created'] ?? 0;
            return $dateB <=> $dateA;
        });

        // Apply pagination to merged results
        $page = $pagination['page'] ?? 1;
        $perPage = $pagination['per_page'] ?? 25;
        $offset = ($page - 1) * $perPage;
        $paginatedTasks = array_slice($allTasks, $offset, $perPage);

        return [
            'success' => true,
            'data' => $paginatedTasks,
            'count' => count($paginatedTasks),
            'total_count' => count($allTasks),
            'source_count' => count($sources),
            'pagination' => [
                'page' => $page,
                'per_page' => $perPage,
                'total' => count($allTasks),
                'has_more' => count($allTasks) > ($offset + $perPage)
            ],
            'filters_applied' => $filters,
            'errors' => $errors,
            'merged_from' => array_column($sources, 'list_name')
        ];
    }

    /**
     * Handle legacy data source for backward compatibility
     */
    protected function getLegacyDataSourceData(string $dataSourceId, array $filters, array $pagination): array
    {
        // This would handle old-style data source IDs like "source_0"
        // For now, return empty result
        return [
            'success' => false,
            'message' => 'Legacy data source not supported',
            'data' => []
        ];
    }

    /**
     * Create a new custom data source
     */
    public function createDataSource(array $data): ClickUpDataSource
    {
        return ClickUpDataSource::create([
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'type' => $this->determineType($data['sources'] ?? []),
            'sources' => $data['sources'] ?? [],
            'configuration' => $data['configuration'] ?? [],
            'enabled' => $data['enabled'] ?? true,
            'sort_order' => $data['sort_order'] ?? 0
        ]);
    }

    /**
     * Update an existing data source
     */
    public function updateDataSource(int $id, array $data): ?ClickUpDataSource
    {
        $dataSource = ClickUpDataSource::find($id);
        
        if (!$dataSource) {
            return null;
        }

        $updateData = array_filter([
            'name' => $data['name'] ?? null,
            'description' => $data['description'] ?? null,
            'sources' => $data['sources'] ?? null,
            'configuration' => $data['configuration'] ?? null,
            'enabled' => $data['enabled'] ?? null,
            'sort_order' => $data['sort_order'] ?? null
        ], function($value) {
            return $value !== null;
        });

        if (isset($updateData['sources'])) {
            $updateData['type'] = $this->determineType($updateData['sources']);
        }

        $dataSource->update($updateData);
        return $dataSource->fresh();
    }

    /**
     * Delete a data source
     */
    public function deleteDataSource(int $id): bool
    {
        $dataSource = ClickUpDataSource::find($id);
        
        if (!$dataSource) {
            return false;
        }

        return $dataSource->delete();
    }

    /**
     * Determine data source type based on sources
     */
    protected function determineType(array $sources): string
    {
        $count = count($sources);
        
        if ($count === 0) {
            return ClickUpDataSource::TYPE_CUSTOM;
        } elseif ($count === 1) {
            return ClickUpDataSource::TYPE_SINGLE;
        } else {
            return ClickUpDataSource::TYPE_COMPOSITE;
        }
    }

    /**
     * Validate data source configuration
     */
    public function validateDataSource(array $data): array
    {
        $errors = [];

        if (empty($data['name'])) {
            $errors[] = 'Data source name is required';
        }

        if (empty($data['sources']) || !is_array($data['sources'])) {
            $errors[] = 'At least one source must be configured';
        } else {
            foreach ($data['sources'] as $index => $source) {
                if (empty($source['list_id'])) {
                    $errors[] = "Source {$index}: List ID is required";
                }
                if (empty($source['list_name'])) {
                    $errors[] = "Source {$index}: List name is required";
                }
            }
        }

        return $errors;
    }

    /**
     * Get data source by ID
     */
    public function getDataSource(string $dataSourceId): ?array
    {
        if (str_starts_with($dataSourceId, 'custom_')) {
            $id = str_replace('custom_', '', $dataSourceId);
            $dataSource = ClickUpDataSource::find($id);
            
            return $dataSource ? $dataSource->toCompatibleArray() : null;
        }

        return null;
    }
}
