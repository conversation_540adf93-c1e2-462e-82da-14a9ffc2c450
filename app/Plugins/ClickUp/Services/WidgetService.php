<?php

namespace App\Plugins\ClickUp\Services;

use App\Plugins\ClickUp\Models\ClickUpPage;

class WidgetService
{
    protected ClickUpService $clickUpService;

    public function __construct(ClickUpService $clickUpService)
    {
        $this->clickUpService = $clickUpService;
    }

    /**
     * Render a widget based on its configuration
     */
    public function renderWidget(array $widgetConfig, ClickUpPage $page): array
    {
        $type = $widgetConfig['type'] ?? 'table';
        
        switch ($type) {
            case 'table':
                return $this->renderTableWidget($widgetConfig, $page);
            case 'number_card':
                return $this->renderNumberCardWidget($widgetConfig, $page);
            case 'progress_card':
                return $this->renderProgressCardWidget($widgetConfig, $page);
            case 'status_chart':
                return $this->renderStatusChartWidget($widgetConfig, $page);
            case 'timeline_card':
                return $this->renderTimelineCardWidget($widgetConfig, $page);
            case 'assignee_chart':
                return $this->renderAssigneeChartWidget($widgetConfig, $page);
            default:
                return $this->renderDefaultWidget($widgetConfig, $page);
        }
    }

    /**
     * Render table widget
     */
    protected function renderTableWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        // Get tasks with filtering and pagination
        $filters = array_merge($page->getGlobalFilters(), $config['filters'] ?? []);
        $pagination = $config['pagination'] ?? ['page' => 1, 'per_page' => 25];
        
        $tasksResult = $this->clickUpService->getListTasks(
            $dataSource['list_id'],
            $filters,
            $pagination
        );

        if (!$tasksResult['success']) {
            return $this->createErrorWidget($tasksResult['message']);
        }

        return [
            'type' => 'table',
            'title' => $config['title'] ?? $dataSource['list_name'],
            'data' => [
                'tasks' => $tasksResult['data'],
                'pagination' => $tasksResult['pagination'] ?? [],
                'columns' => $config['columns'] ?? $this->getDefaultTableColumns(),
                'filters' => $filters,
                'sortable' => $config['sortable'] ?? true,
                'searchable' => $config['searchable'] ?? true,
                'exportable' => $config['exportable'] ?? true
            ],
            'config' => $config
        ];
    }

    /**
     * Render number card widget
     */
    protected function renderNumberCardWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        $metric = $config['metric'] ?? 'total_tasks';
        $value = $this->calculateMetric($dataSource, $metric, $page->getGlobalFilters());

        return [
            'type' => 'number_card',
            'title' => $config['title'] ?? $this->getMetricTitle($metric),
            'data' => [
                'value' => $value,
                'metric' => $metric,
                'format' => $config['format'] ?? 'number',
                'icon' => $config['icon'] ?? $this->getMetricIcon($metric),
                'color' => $config['color'] ?? $this->getMetricColor($metric)
            ],
            'config' => $config
        ];
    }

    /**
     * Render progress card widget
     */
    protected function renderProgressCardWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        $progressData = $this->calculateProgress($dataSource, $page->getGlobalFilters());

        return [
            'type' => 'progress_card',
            'title' => $config['title'] ?? 'Progress Overview',
            'data' => [
                'total' => $progressData['total'],
                'completed' => $progressData['completed'],
                'percentage' => $progressData['percentage'],
                'in_progress' => $progressData['in_progress'],
                'remaining' => $progressData['remaining']
            ],
            'config' => $config
        ];
    }

    /**
     * Render status chart widget
     */
    protected function renderStatusChartWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        $statusData = $this->getStatusDistribution($dataSource, $page->getGlobalFilters());

        return [
            'type' => 'status_chart',
            'title' => $config['title'] ?? 'Status Distribution',
            'data' => [
                'chart_type' => $config['chart_type'] ?? 'pie',
                'statuses' => $statusData,
                'colors' => $this->getStatusColors()
            ],
            'config' => $config
        ];
    }

    /**
     * Render timeline card widget
     */
    protected function renderTimelineCardWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        $timelineData = $this->getTimelineData($dataSource, $page->getGlobalFilters());

        return [
            'type' => 'timeline_card',
            'title' => $config['title'] ?? 'Upcoming Deadlines',
            'data' => [
                'upcoming' => $timelineData['upcoming'],
                'overdue' => $timelineData['overdue'],
                'this_week' => $timelineData['this_week'],
                'next_week' => $timelineData['next_week']
            ],
            'config' => $config
        ];
    }

    /**
     * Render assignee chart widget
     */
    protected function renderAssigneeChartWidget(array $config, ClickUpPage $page): array
    {
        $dataSourceId = $config['data_source_id'] ?? null;
        $dataSource = $this->getDataSourceById($page, $dataSourceId);
        
        if (!$dataSource) {
            return $this->createErrorWidget('Data source not found');
        }

        $assigneeData = $this->getAssigneeDistribution($dataSource, $page->getGlobalFilters());

        return [
            'type' => 'assignee_chart',
            'title' => $config['title'] ?? 'Tasks by Assignee',
            'data' => [
                'chart_type' => $config['chart_type'] ?? 'bar',
                'assignees' => $assigneeData
            ],
            'config' => $config
        ];
    }

    /**
     * Render default widget for unknown types
     */
    protected function renderDefaultWidget(array $config, ClickUpPage $page): array
    {
        return [
            'type' => 'error',
            'title' => 'Unknown Widget Type',
            'data' => [
                'message' => 'Widget type "' . ($config['type'] ?? 'unknown') . '" is not supported'
            ],
            'config' => $config
        ];
    }

    /**
     * Create error widget
     */
    protected function createErrorWidget(string $message): array
    {
        return [
            'type' => 'error',
            'title' => 'Error',
            'data' => ['message' => $message],
            'config' => []
        ];
    }

    /**
     * Get data source by ID from page
     */
    protected function getDataSourceById(ClickUpPage $page, ?string $dataSourceId): ?array
    {
        if (!$dataSourceId) {
            return null;
        }

        $dataSources = $page->getDataSources();
        foreach ($dataSources as $index => $source) {
            if (($source['id'] ?? $index) == $dataSourceId) {
                return $source;
            }
        }

        return null;
    }

    /**
     * Get default table columns
     */
    protected function getDefaultTableColumns(): array
    {
        return [
            ['key' => 'name', 'label' => 'Task', 'sortable' => true, 'visible' => true],
            ['key' => 'status', 'label' => 'Status', 'sortable' => true, 'visible' => true],
            ['key' => 'assignee', 'label' => 'Assignee', 'sortable' => true, 'visible' => true],
            ['key' => 'due_date', 'label' => 'Due Date', 'sortable' => true, 'visible' => true],
            ['key' => 'priority', 'label' => 'Priority', 'sortable' => true, 'visible' => true]
        ];
    }

    /**
     * Calculate metric value
     */
    protected function calculateMetric(array $dataSource, string $metric, array $filters): int
    {
        // This would be implemented with actual ClickUp API calls
        // For now, return mock data
        switch ($metric) {
            case 'total_tasks':
                return 42;
            case 'completed_tasks':
                return 28;
            case 'overdue_tasks':
                return 5;
            case 'in_progress_tasks':
                return 9;
            default:
                return 0;
        }
    }

    /**
     * Get metric title
     */
    protected function getMetricTitle(string $metric): string
    {
        $titles = [
            'total_tasks' => 'Total Tasks',
            'completed_tasks' => 'Completed',
            'overdue_tasks' => 'Overdue',
            'in_progress_tasks' => 'In Progress'
        ];

        return $titles[$metric] ?? ucwords(str_replace('_', ' ', $metric));
    }

    /**
     * Get metric icon
     */
    protected function getMetricIcon(string $metric): string
    {
        $icons = [
            'total_tasks' => 'fas fa-tasks',
            'completed_tasks' => 'fas fa-check-circle',
            'overdue_tasks' => 'fas fa-exclamation-triangle',
            'in_progress_tasks' => 'fas fa-clock'
        ];

        return $icons[$metric] ?? 'fas fa-hashtag';
    }

    /**
     * Get metric color
     */
    protected function getMetricColor(string $metric): string
    {
        $colors = [
            'total_tasks' => 'blue',
            'completed_tasks' => 'green',
            'overdue_tasks' => 'red',
            'in_progress_tasks' => 'yellow'
        ];

        return $colors[$metric] ?? 'gray';
    }

    /**
     * Calculate progress data
     */
    protected function calculateProgress(array $dataSource, array $filters): array
    {
        // Mock implementation - would use real ClickUp API
        return [
            'total' => 50,
            'completed' => 32,
            'in_progress' => 12,
            'remaining' => 6,
            'percentage' => 64
        ];
    }

    /**
     * Get status distribution
     */
    protected function getStatusDistribution(array $dataSource, array $filters): array
    {
        // Mock implementation - would use real ClickUp API
        return [
            ['status' => 'To Do', 'count' => 15, 'percentage' => 30],
            ['status' => 'In Progress', 'count' => 20, 'percentage' => 40],
            ['status' => 'Review', 'count' => 10, 'percentage' => 20],
            ['status' => 'Done', 'count' => 5, 'percentage' => 10]
        ];
    }

    /**
     * Get status colors
     */
    protected function getStatusColors(): array
    {
        return [
            'To Do' => '#6B7280',
            'In Progress' => '#F59E0B',
            'Review' => '#8B5CF6',
            'Done' => '#10B981'
        ];
    }

    /**
     * Get timeline data
     */
    protected function getTimelineData(array $dataSource, array $filters): array
    {
        // Mock implementation - would use real ClickUp API
        return [
            'upcoming' => 8,
            'overdue' => 3,
            'this_week' => 12,
            'next_week' => 7
        ];
    }

    /**
     * Get assignee distribution
     */
    protected function getAssigneeDistribution(array $dataSource, array $filters): array
    {
        // Mock implementation - would use real ClickUp API
        return [
            ['name' => 'John Doe', 'count' => 15],
            ['name' => 'Jane Smith', 'count' => 12],
            ['name' => 'Bob Johnson', 'count' => 8],
            ['name' => 'Unassigned', 'count' => 5]
        ];
    }
}
