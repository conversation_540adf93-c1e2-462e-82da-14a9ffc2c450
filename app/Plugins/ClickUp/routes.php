<?php

use App\Plugins\ClickUp\Controllers\ClickUpController;
use Illuminate\Support\Facades\Route;

// ClickUp Plugin Routes
Route::get('/metrics', [ClickUpController::class, 'getMetrics']);
Route::get('/tasks', [ClickUpController::class, 'getTasks']);
Route::get('/progress', [ClickUpController::class, 'getProgress']);
Route::get('/overdue', [ClickUpController::class, 'getOverdue']);
Route::get('/productivity', [ClickUpController::class, 'getProductivity']);
Route::get('/tasks-by-status', [ClickUpController::class, 'getTasksByStatus']);
Route::get('/spaces', [ClickUpController::class, 'getSpaces']);
Route::get('/spaces-test-failure', [ClickUpController::class, 'getSpacesTestFailure']);
Route::get('/test-connection', [ClickUpController::class, 'testConnection']);
