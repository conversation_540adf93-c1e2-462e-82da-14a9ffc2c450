<?php

use App\Plugins\ClickUp\Controllers\ClickUpController;
use App\Plugins\ClickUp\Controllers\PageManagementController;
use Illuminate\Support\Facades\Route;

// ClickUp Plugin Routes
Route::get('/metrics', [ClickUpController::class, 'getMetrics']);
Route::get('/tasks', [ClickUpController::class, 'getTasks']);
Route::get('/progress', [ClickUpController::class, 'getProgress']);
Route::get('/overdue', [ClickUpController::class, 'getOverdue']);
Route::get('/productivity', [ClickUpController::class, 'getProductivity']);
Route::get('/tasks-by-status', [ClickUpController::class, 'getTasksByStatus']);
Route::get('/spaces', [ClickUpController::class, 'getSpaces']);
Route::get('/spaces-test-failure', [ClickUpController::class, 'getSpacesTestFailure']);
Route::get('/test-connection', [ClickUpController::class, 'testConnection']);
Route::get('/lists', [ClickUpController::class, 'getLists']);
Route::get('/folders', [ClickUpController::class, 'getFolders']);
Route::get('/available-spaces', [ClickUpController::class, 'getAvailableSpaces']);
Route::get('/space-hierarchy/{spaceId}', [ClickUpController::class, 'getSpaceHierarchy']);

// Page Management API Routes
Route::prefix('pages')->group(function () {
    Route::get('/', [PageManagementController::class, 'index']);
    Route::post('/', [PageManagementController::class, 'store']);
    Route::put('/{page}', [PageManagementController::class, 'update']);
    Route::delete('/{page}', [PageManagementController::class, 'destroy']);
    Route::post('/reorder', [PageManagementController::class, 'updateOrder']);
});
