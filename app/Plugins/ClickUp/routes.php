<?php

use App\Plugins\ClickUp\Controllers\ClickUpController;
use App\Plugins\ClickUp\Controllers\PageManagementController;
use Illuminate\Support\Facades\Route;

// ClickUp Plugin Routes
Route::get('/metrics', [ClickUpController::class, 'getMetrics']);
Route::get('/tasks', [ClickUpController::class, 'getTasks']);
Route::get('/progress', [ClickUpController::class, 'getProgress']);
Route::get('/overdue', [ClickUpController::class, 'getOverdue']);
Route::get('/productivity', [ClickUpController::class, 'getProductivity']);
Route::get('/tasks-by-status', [ClickUpController::class, 'getTasksByStatus']);
Route::get('/spaces', [ClickUpController::class, 'getSpaces']);
Route::get('/spaces-test-failure', [ClickUpController::class, 'getSpacesTestFailure']);
Route::get('/test-connection', [ClickUpController::class, 'testConnection']);
Route::get('/lists', [ClickUpController::class, 'getLists']);
Route::get('/folders', [ClickUpController::class, 'getFolders']);
Route::get('/available-spaces', [ClickUpController::class, 'getAvailableSpaces']);
Route::get('/space-hierarchy/{spaceId}', [ClickUpController::class, 'getSpaceHierarchy']);

// Page Builder API routes
Route::prefix('builder')->group(function () {
    Route::get('/{pageId}/data', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'getBuilderData']);
    Route::post('/{pageId}/widgets', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'addWidget']);
    Route::put('/{pageId}/widgets/{widgetId}', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'updateWidget']);
    Route::delete('/{pageId}/widgets/{widgetId}', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'removeWidget']);
    Route::put('/{pageId}/layout', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'updateLayout']);
    Route::get('/{pageId}/widgets/{widgetId}/render', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'renderWidget']);
    Route::get('/{pageId}/preview', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'preview']);
    Route::post('/{pageId}/publish', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'publish']);
    Route::post('/{pageId}/unpublish', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'unpublish']);
    Route::get('/{pageId}/data-sources', [\App\Plugins\ClickUp\Controllers\PageBuilderController::class, 'getDataSources']);
});

// Page Management API Routes
Route::prefix('pages')->group(function () {
    Route::get('/', [PageManagementController::class, 'index']);
    Route::post('/', [PageManagementController::class, 'store']);
    Route::put('/{page}', [PageManagementController::class, 'update']);
    Route::delete('/{page}', [PageManagementController::class, 'destroy']);
    Route::post('/reorder', [PageManagementController::class, 'updateOrder']);
});
