# ClickUp Plugin

A comprehensive ClickUp integration plugin for the Product Intelligence Hub that provides task management, progress tracking, and team productivity insights.

## Features

### Dashboard Widgets
- **ClickUp Metrics**: Overview of key performance indicators
- **Task Progress**: Visual progress tracking for active tasks
- **Overdue Tasks**: Alert system for overdue items

### Sidebar Navigation
- **Tasks**: Complete task management interface
- **Progress**: Team and project progress visualization
- **Team**: Team member productivity and workload
- **Overdue**: Overdue task management and alerts

### API Endpoints
- `/api/plugins/clickup/metrics` - Get key metrics and KPIs
- `/api/plugins/clickup/tasks` - Retrieve task data
- `/api/plugins/clickup/progress` - Get progress information
- `/api/plugins/clickup/overdue` - List overdue tasks
- `/api/plugins/clickup/productivity` - Team productivity metrics
- `/api/plugins/clickup/tasks-by-status` - Tasks grouped by status
- `/api/plugins/clickup/spaces` - Available ClickUp spaces
- `/api/plugins/clickup/test-connection` - Test API connectivity

## Configuration

### Required Settings
- **API Token**: Your ClickUp API token for authentication
- **Space ID**: ClickUp Space ID to sync with (optional)
- **Team ID**: ClickUp Team ID (optional)
- **Sync Interval**: Data synchronization frequency in minutes (default: 30)

### Setup Instructions
1. Obtain your ClickUp API token from ClickUp settings
2. Configure the plugin through the settings page
3. Test the connection using the test endpoint
4. Set up your preferred sync interval

## Database Schema

The plugin creates the following database table:
- `clickup_tasks`: Stores synchronized task data from ClickUp

## Usage

### Widget Integration
The plugin automatically registers widgets that appear on the dashboard:
- Large metrics widget showing key performance indicators
- Medium progress widget with visual progress bars
- Small overdue tasks widget for quick alerts

### Navigation Integration
Sidebar navigation items are automatically added when the plugin is enabled:
- Tasks section for comprehensive task management
- Progress section for tracking project advancement
- Team section for monitoring team productivity
- Overdue section for managing delayed tasks

### API Integration
All endpoints are automatically registered under `/api/plugins/clickup/` prefix and can be used by frontend components or external integrations.

## Development

### File Structure
```
app/Plugins/ClickUp/
├── ClickUpPlugin.php          # Main plugin class
├── Controllers/
│   └── ClickUpController.php  # API endpoints controller
├── Services/
│   └── ClickUpService.php     # Business logic service
├── migrations/
│   └── create_clickup_tasks_table.php  # Database migration
├── views/
│   └── settings.blade.php     # Plugin settings interface
├── routes.php                 # Plugin routes definition
└── README.md                  # This documentation
```

### Configuration Schema
The plugin defines a comprehensive configuration schema for the settings interface, including validation rules and field descriptions.

## Version History

- **v1.0.0**: Initial release with core functionality
  - Dashboard widgets
  - Sidebar navigation
  - API endpoints
  - Settings interface
  - Database integration
