# ClickUp Plugin for Product Intelligence Hub

## Overview

The ClickUp plugin integrates with ClickUp's API to provide task management insights and team productivity metrics for the Product Intelligence Hub dashboard.

## Features

- **Task Progress Tracking**: Monitor completion rates and task status distribution
- **Overdue Task Alerts**: Identify and highlight overdue tasks requiring attention
- **Team Productivity Metrics**: Track team performance and completion rates
- **Real-time Data**: Cached API responses for optimal performance
- **Error Handling**: Graceful fallbacks when API is unavailable

## Configuration

### Required Settings

1. **API Token**: Your ClickUp API token (pre-configured: `pk_707692_VG30E6E0O5BPKGW98T1TWSZV70AC45XI`)
2. **Space ID**: The ClickUp Space ID to manage (required - must be configured by user)

### Optional Settings

3. **Team ID**: Additional filtering by team (optional)
4. **Sync Interval**: How often to refresh data (default: 30 minutes)

### Setup Instructions

1. Go to Settings page: `/settings`
2. Find the ClickUp plugin configuration section
3. Enter your ClickUp Space ID (required)
4. Optionally configure Team ID for additional filtering
5. Save settings

## API Endpoints

All endpoints follow the pattern: `/api/plugins/clickup/{endpoint}`

- `GET /metrics` - Overall task metrics and dashboard data
- `GET /tasks` - Task list with optional filters
- `GET /progress` - Task progress analytics
- `GET /overdue` - Overdue tasks list
- `GET /productivity` - Team productivity metrics
- `GET /tasks-by-status` - Tasks grouped by status
- `GET /test-connection` - Test API connectivity

## Dashboard Widgets

### 1. Task Progress Overview
- Shows completion rate with visual progress bar
- Displays completed vs in-progress task counts
- Size: Medium

### 2. Overdue Tasks Alert
- Highlights number of overdue tasks
- Shows most urgent overdue items
- Color-coded alerts (red for overdue, green for none)
- Size: Medium

### 3. Team Productivity Metrics
- Weekly and monthly completion statistics
- Average team completion rate
- Top performer rankings
- Size: Large (spans 2 columns)

## Error Handling

The plugin includes comprehensive error handling:

- **Missing Configuration**: Returns default empty metrics
- **API Failures**: Graceful fallbacks with error messages
- **Rate Limiting**: Cached responses to minimize API calls
- **Network Issues**: Timeout handling and retry logic

## Data Caching

- API responses are cached for 5 minutes
- Reduces API rate limit usage
- Improves dashboard loading performance
- Cache automatically refreshes on data updates

## Status Mapping

ClickUp task statuses are mapped to standard categories:

- **Open**: New, To Do, Open
- **In Progress**: In Progress, In Review
- **Completed**: Complete, Closed, Done
- **Overdue**: Any task past due date (regardless of status)

## Testing

Use the test connection endpoint to verify setup:

```bash
curl http://localhost:8000/api/plugins/clickup/test-connection
```

Expected response when properly configured:
```json
{
  "success": true,
  "user": {...},
  "message": "Successfully connected to ClickUp API"
}
```

## Troubleshooting

### Common Issues

1. **"ClickUp API token not configured"**
   - Check that API token is set in plugin configuration

2. **"ClickUp Space ID not configured"**
   - Space ID is required - configure in Settings page

3. **API request failed: 401**
   - Invalid API token - verify token is correct

4. **API request failed: 404**
   - Invalid Space ID - verify Space ID exists and is accessible

5. **Empty data returned**
   - Space may have no tasks, or user may not have access

### Getting Space ID

1. Go to your ClickUp workspace
2. Navigate to the desired Space
3. Check the URL - Space ID is the number after `/space/`
4. Example: `https://app.clickup.com/space/12345` → Space ID is `12345`
