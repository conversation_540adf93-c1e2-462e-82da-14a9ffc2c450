<!-- Number Card Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
    <div class="p-6 h-full flex flex-col justify-center">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                @php
                    $colorClasses = [
                        'blue' => 'bg-blue-100 text-blue-600',
                        'green' => 'bg-green-100 text-green-600',
                        'red' => 'bg-red-100 text-red-600',
                        'yellow' => 'bg-yellow-100 text-yellow-600',
                        'purple' => 'bg-purple-100 text-purple-600',
                        'gray' => 'bg-gray-100 text-gray-600'
                    ];
                    $colorClass = $colorClasses[$widget['data']['color']] ?? $colorClasses['blue'];
                @endphp
                <div class="w-12 h-12 rounded-lg flex items-center justify-center {{ $colorClass }}">
                    <i class="{{ $widget['data']['icon'] }} text-xl"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-500 mb-1">{{ $widget['title'] }}</p>
                <div class="flex items-baseline">
                    @if($widget['data']['format'] === 'percentage')
                        <p class="text-3xl font-bold text-gray-900">{{ $widget['data']['value'] }}%</p>
                    @elseif($widget['data']['format'] === 'currency')
                        <p class="text-3xl font-bold text-gray-900">${{ number_format($widget['data']['value']) }}</p>
                    @else
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($widget['data']['value']) }}</p>
                    @endif
                </div>
                @if(isset($widget['data']['change']))
                    <div class="mt-2 flex items-center">
                        @if($widget['data']['change'] > 0)
                            <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                            <span class="text-sm text-green-600">+{{ $widget['data']['change'] }}%</span>
                        @elseif($widget['data']['change'] < 0)
                            <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                            <span class="text-sm text-red-600">{{ $widget['data']['change'] }}%</span>
                        @else
                            <i class="fas fa-minus text-gray-500 text-sm mr-1"></i>
                            <span class="text-sm text-gray-600">No change</span>
                        @endif
                        <span class="text-sm text-gray-500 ml-1">from last period</span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
