<!-- Assignee Chart Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
    <div class="p-6 h-full">
        <h3 class="text-lg font-medium text-gray-900 mb-6">{{ $widget['title'] }}</h3>
        
        @if($widget['data']['chart_type'] === 'bar')
            <!-- Horizontal Bar Chart -->
            <div class="space-y-4">
                @php
                    $maxCount = max(array_column($widget['data']['assignees'], 'count'));
                @endphp
                @foreach($widget['data']['assignees'] as $index => $assignee)
                    @php
                        $percentage = $maxCount > 0 ? ($assignee['count'] / $maxCount) * 100 : 0;
                        $colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-red-500', 'bg-indigo-500'];
                        $colorClass = $colors[$index % count($colors)];
                    @endphp
                    <div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <div class="flex items-center">
                                @if($assignee['name'] !== 'Unassigned')
                                    <div class="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center mr-2">
                                        <span class="text-xs text-gray-600">{{ substr($assignee['name'], 0, 1) }}</span>
                                    </div>
                                @else
                                    <div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                                        <i class="fas fa-user-slash text-xs text-gray-400"></i>
                                    </div>
                                @endif
                                <span class="text-gray-700 font-medium">{{ $assignee['name'] }}</span>
                            </div>
                            <span class="font-bold text-gray-900">{{ $assignee['count'] }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="h-3 rounded-full transition-all duration-500 ease-out {{ $colorClass }}" 
                                 style="width: {{ $percentage }}%"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- List View -->
            <div class="space-y-3">
                @foreach($widget['data']['assignees'] as $assignee)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            @if($assignee['name'] !== 'Unassigned')
                                <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                    <span class="text-sm text-gray-600 font-medium">{{ substr($assignee['name'], 0, 1) }}</span>
                                </div>
                            @else
                                <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                    <i class="fas fa-user-slash text-gray-400"></i>
                                </div>
                            @endif
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ $assignee['name'] }}</div>
                                @if($assignee['name'] !== 'Unassigned')
                                    <div class="text-xs text-gray-500">Team Member</div>
                                @else
                                    <div class="text-xs text-gray-500">No assignee</div>
                                @endif
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-gray-900">{{ $assignee['count'] }}</div>
                            <div class="text-xs text-gray-500">tasks</div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
        
        <!-- Summary -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            @php
                $totalTasks = array_sum(array_column($widget['data']['assignees'], 'count'));
                $assignedTasks = $totalTasks - ($widget['data']['assignees'][array_search('Unassigned', array_column($widget['data']['assignees'], 'name'))] ?? ['count' => 0])['count'];
                $assignmentRate = $totalTasks > 0 ? round(($assignedTasks / $totalTasks) * 100) : 0;
            @endphp
            
            <div class="grid grid-cols-2 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-gray-900">{{ $totalTasks }}</div>
                    <div class="text-sm text-gray-500">Total Tasks</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600">{{ $assignmentRate }}%</div>
                    <div class="text-sm text-gray-500">Assigned</div>
                </div>
            </div>
            
            <!-- Assignment Progress -->
            <div class="mt-4">
                <div class="flex items-center justify-between text-sm mb-1">
                    <span class="text-gray-600">Assignment Rate</span>
                    <span class="font-medium text-gray-900">{{ $assignedTasks }}/{{ $totalTasks }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full transition-all duration-500 ease-out" 
                         style="width: {{ $assignmentRate }}%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
