<!-- Table Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
    <!-- Widget Header -->
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">{{ $widget['title'] }}</h3>
        <div class="flex items-center space-x-2">
            @if($widget['data']['searchable'] ?? true)
                <div class="relative">
                    <input type="text" 
                           placeholder="Search tasks..." 
                           class="block w-48 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            @endif
            @if($widget['data']['exportable'] ?? true)
                <button class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            @endif
        </div>
    </div>

    <!-- Table Content -->
    <div class="flex-1 overflow-hidden">
        <div class="overflow-x-auto h-full">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 sticky top-0">
                    <tr>
                        @foreach($widget['data']['columns'] as $column)
                            @if($column['visible'])
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-1">
                                        <span>{{ $column['label'] }}</span>
                                        @if($column['sortable'])
                                            <i class="fas fa-sort text-gray-400 cursor-pointer hover:text-gray-600"></i>
                                        @endif
                                    </div>
                                </th>
                            @endif
                        @endforeach
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($widget['data']['tasks'] as $task)
                        <tr class="hover:bg-gray-50">
                            @foreach($widget['data']['columns'] as $column)
                                @if($column['visible'])
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($column['key'] === 'name')
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ $task['name'] }}</div>
                                                    @if(!empty($task['description']))
                                                        <div class="text-sm text-gray-500">{{ Str::limit($task['description'], 60) }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        @elseif($column['key'] === 'status')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                  style="background-color: {{ $task['status']['color'] }}20; color: {{ $task['status']['color'] }};">
                                                {{ $task['status']['status'] }}
                                            </span>
                                        @elseif($column['key'] === 'assignee')
                                            @if(!empty($task['assignees']))
                                                <div class="flex items-center">
                                                    @if($task['assignees'][0]['profilePicture'])
                                                        <img class="h-6 w-6 rounded-full mr-2" 
                                                             src="{{ $task['assignees'][0]['profilePicture'] }}" 
                                                             alt="{{ $task['assignees'][0]['username'] }}">
                                                    @else
                                                        <div class="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center mr-2">
                                                            <span class="text-xs text-gray-600">{{ substr($task['assignees'][0]['username'], 0, 1) }}</span>
                                                        </div>
                                                    @endif
                                                    <span class="text-sm text-gray-900">{{ $task['assignees'][0]['username'] }}</span>
                                                </div>
                                            @else
                                                <span class="text-sm text-gray-500">Unassigned</span>
                                            @endif
                                        @elseif($column['key'] === 'due_date')
                                            @if($task['due_date'])
                                                @php
                                                    $dueDate = \Carbon\Carbon::createFromTimestamp($task['due_date'] / 1000);
                                                    $isOverdue = $dueDate->isPast();
                                                @endphp
                                                <span class="text-sm {{ $isOverdue ? 'text-red-600' : 'text-gray-900' }}">
                                                    {{ $dueDate->format('M j, Y') }}
                                                </span>
                                            @else
                                                <span class="text-sm text-gray-500">-</span>
                                            @endif
                                        @elseif($column['key'] === 'priority')
                                            @if($task['priority']['priority'])
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                                                      style="background-color: {{ $task['priority']['color'] }}20; color: {{ $task['priority']['color'] }};">
                                                    {{ ucfirst($task['priority']['priority']) }}
                                                </span>
                                            @else
                                                <span class="text-sm text-gray-500">-</span>
                                            @endif
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @empty
                        <tr>
                            <td colspan="{{ count(array_filter($widget['data']['columns'], fn($col) => $col['visible'])) }}" 
                                class="px-6 py-12 text-center text-sm text-gray-500">
                                <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                                <div>No tasks found</div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($widget['data']['pagination']) && $widget['data']['pagination']['total'] > $widget['data']['pagination']['per_page'])
        <div class="px-6 py-3 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ $widget['data']['pagination']['per_page'] }} of {{ $widget['data']['pagination']['total'] }} tasks
                </div>
                <div class="flex items-center space-x-2">
                    <button class="px-3 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                            {{ $widget['data']['pagination']['page'] <= 1 ? 'disabled' : '' }}>
                        Previous
                    </button>
                    <span class="px-3 py-1 text-sm text-gray-700">
                        Page {{ $widget['data']['pagination']['page'] }}
                    </span>
                    <button class="px-3 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                            {{ !$widget['data']['pagination']['has_more'] ? 'disabled' : '' }}>
                        Next
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>
