<!-- Timeline Card Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
    <div class="p-6 h-full">
        <h3 class="text-lg font-medium text-gray-900 mb-6">{{ $widget['title'] }}</h3>
        
        <!-- Timeline Stats Grid -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <!-- Upcoming Tasks -->
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ $widget['data']['upcoming'] }}</div>
                <div class="text-sm text-blue-700 font-medium">Upcoming</div>
                <div class="text-xs text-blue-600 mt-1">Next 7 days</div>
            </div>
            
            <!-- Overdue Tasks -->
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ $widget['data']['overdue'] }}</div>
                <div class="text-sm text-red-700 font-medium">Overdue</div>
                <div class="text-xs text-red-600 mt-1">Past due date</div>
            </div>
        </div>
        
        <!-- Weekly Breakdown -->
        <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span class="text-sm font-medium text-gray-700">This Week</span>
                </div>
                <span class="text-lg font-bold text-gray-900">{{ $widget['data']['this_week'] }}</span>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span class="text-sm font-medium text-gray-700">Next Week</span>
                </div>
                <span class="text-lg font-bold text-gray-900">{{ $widget['data']['next_week'] }}</span>
            </div>
        </div>
        
        <!-- Progress Indicator -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            @php
                $totalTasks = $widget['data']['upcoming'] + $widget['data']['overdue'] + $widget['data']['this_week'] + $widget['data']['next_week'];
                $urgentTasks = $widget['data']['overdue'] + $widget['data']['this_week'];
                $urgencyPercentage = $totalTasks > 0 ? round(($urgentTasks / $totalTasks) * 100) : 0;
            @endphp
            
            <div class="flex items-center justify-between text-sm mb-2">
                <span class="text-gray-600">Urgency Level</span>
                <span class="font-medium {{ $urgencyPercentage > 70 ? 'text-red-600' : ($urgencyPercentage > 40 ? 'text-yellow-600' : 'text-green-600') }}">
                    {{ $urgencyPercentage }}%
                </span>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="h-2 rounded-full transition-all duration-500 ease-out {{ $urgencyPercentage > 70 ? 'bg-red-500' : ($urgencyPercentage > 40 ? 'bg-yellow-500' : 'bg-green-500') }}" 
                     style="width: {{ $urgencyPercentage }}%"></div>
            </div>
            
            <div class="text-xs text-gray-500 mt-2">
                {{ $urgentTasks }} of {{ $totalTasks }} tasks need immediate attention
            </div>
        </div>
    </div>
</div>
