<!-- Status Chart Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
    <div class="p-6 h-full">
        <h3 class="text-lg font-medium text-gray-900 mb-6">{{ $widget['title'] }}</h3>
        
        @if($widget['data']['chart_type'] === 'pie')
            <!-- Pie Chart Representation -->
            <div class="flex items-center justify-center mb-6">
                <div class="relative w-32 h-32">
                    <!-- Simple CSS-based pie chart representation -->
                    <div class="w-full h-full rounded-full border-8 border-gray-200 relative overflow-hidden">
                        @php
                            $cumulativePercentage = 0;
                        @endphp
                        @foreach($widget['data']['statuses'] as $index => $status)
                            @php
                                $rotation = $cumulativePercentage * 3.6; // Convert percentage to degrees
                                $cumulativePercentage += $status['percentage'];
                            @endphp
                            <div class="absolute inset-0 rounded-full"
                                 style="background: conic-gradient(from {{ $rotation }}deg, {{ $widget['data']['colors'][$status['status']] ?? '#6B7280' }} 0deg {{ $status['percentage'] * 3.6 }}deg, transparent {{ $status['percentage'] * 3.6 }}deg);">
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <!-- Bar Chart Representation -->
            <div class="space-y-3 mb-6">
                @foreach($widget['data']['statuses'] as $status)
                    <div>
                        <div class="flex items-center justify-between text-sm mb-1">
                            <span class="text-gray-700">{{ $status['status'] }}</span>
                            <span class="font-medium">{{ $status['count'] }} ({{ $status['percentage'] }}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full transition-all duration-500 ease-out" 
                                 style="width: {{ $status['percentage'] }}%; background-color: {{ $widget['data']['colors'][$status['status']] ?? '#6B7280' }};"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
        
        <!-- Legend -->
        <div class="space-y-2">
            @foreach($widget['data']['statuses'] as $status)
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full mr-3" 
                         style="background-color: {{ $widget['data']['colors'][$status['status']] ?? '#6B7280' }};"></div>
                    <span class="text-sm text-gray-700 flex-1">{{ $status['status'] }}</span>
                    <span class="text-sm font-medium text-gray-900">{{ $status['count'] }}</span>
                </div>
            @endforeach
        </div>
        
        <!-- Total -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Total Tasks</span>
                <span class="text-lg font-bold text-gray-900">{{ array_sum(array_column($widget['data']['statuses'], 'count')) }}</span>
            </div>
        </div>
    </div>
</div>
