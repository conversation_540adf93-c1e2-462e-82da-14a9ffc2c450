@extends('clickup::layout')

@section('page-content')
<!-- Team Performance Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    @php
        $weeklyCompleted = $productivity['weekly_completed'] ?? 0;
        $monthlyCompleted = $productivity['monthly_completed'] ?? 0;
        $avgCompletionRate = $productivity['avg_completion_rate'] ?? 0;
        $assigneeStats = $productivity['assignee_stats'] ?? [];
        $totalAssignees = count($assigneeStats);
    @endphp
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-blue-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Team Members</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $totalAssignees }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar-week text-green-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Weekly Completed</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $weeklyCompleted }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar-alt text-purple-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Monthly Completed</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $monthlyCompleted }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line text-yellow-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg Completion Rate</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ round($avgCompletionRate, 1) }}%</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Team Performance Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Team Productivity Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Team Productivity</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="productivityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Task Distribution Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Task Distribution by Assignee</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="distributionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Team Member Performance -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Team Member Performance</h3>
    </div>
    <div class="overflow-hidden">
        @if(count($assigneeStats) > 0)
            <ul class="divide-y divide-gray-200">
                @foreach($assigneeStats as $assignee => $stats)
                    @php
                        $completionRate = ($stats['total'] ?? 0) > 0 ? round(($stats['completed'] ?? 0) / $stats['total'] * 100, 1) : 0;
                        $performanceColor = $completionRate >= 80 ? 'text-green-600' : ($completionRate >= 60 ? 'text-yellow-600' : 'text-red-600');
                        $performanceIcon = $completionRate >= 80 ? 'fa-arrow-up' : ($completionRate >= 60 ? 'fa-arrow-right' : 'fa-arrow-down');
                    @endphp
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4 min-w-0 flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">
                                        {{ $assignee ?: 'Unassigned' }}
                                    </h4>
                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                        <span>{{ $stats['completed'] ?? 0 }}/{{ $stats['total'] ?? 0 }} tasks completed</span>
                                        <span class="mx-2">•</span>
                                        <span class="{{ $performanceColor }}">
                                            <i class="fas {{ $performanceIcon }} mr-1"></i>
                                            {{ $completionRate }}% completion rate
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ml-4">
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $completionRate }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">{{ $completionRate }}%</span>
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        @else
            <div class="px-6 py-12 text-center">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No team data found</h3>
                <p class="text-gray-500">
                    @if(isset($error))
                        There was an error loading team data. Please check your ClickUp configuration.
                    @else
                        No team members found with assigned tasks.
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>

<!-- Team Insights -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Team Insights & Recommendations</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-3">Performance Analysis</h4>
                <div class="space-y-3">
                    @if($avgCompletionRate >= 80)
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-trophy mr-2"></i>
                            <span class="text-sm">Excellent team performance! {{ round($avgCompletionRate, 1) }}% average completion rate</span>
                        </div>
                    @elseif($avgCompletionRate >= 60)
                        <div class="flex items-center text-yellow-600">
                            <i class="fas fa-thumbs-up mr-2"></i>
                            <span class="text-sm">Good team performance. {{ round($avgCompletionRate, 1) }}% average completion rate</span>
                        </div>
                    @else
                        <div class="flex items-center text-red-600">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span class="text-sm">Team performance needs attention. {{ round($avgCompletionRate, 1) }}% average completion rate</span>
                        </div>
                    @endif
                    
                    @if($weeklyCompleted > 0)
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-calendar-check mr-2"></i>
                            <span class="text-sm">{{ $weeklyCompleted }} tasks completed this week</span>
                        </div>
                    @endif
                    
                    @if($totalAssignees > 0)
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-users mr-2"></i>
                            <span class="text-sm">{{ $totalAssignees }} active team members</span>
                        </div>
                    @endif
                </div>
            </div>
            
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-3">Recommendations</h4>
                <div class="space-y-3">
                    @if($avgCompletionRate < 60)
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                            <span class="text-sm">Consider redistributing workload among team members</span>
                        </div>
                    @endif
                    
                    @if($totalAssignees > 0)
                        @php
                            $topPerformer = collect($assigneeStats)->map(function($stats, $assignee) {
                                $rate = ($stats['total'] ?? 0) > 0 ? ($stats['completed'] ?? 0) / $stats['total'] * 100 : 0;
                                return ['assignee' => $assignee, 'rate' => $rate];
                            })->sortByDesc('rate')->first();
                        @endphp
                        
                        @if($topPerformer && $topPerformer['rate'] > 80)
                            <div class="flex items-start text-gray-600">
                                <i class="fas fa-star mr-2 mt-0.5"></i>
                                <span class="text-sm">{{ $topPerformer['assignee'] }} is the top performer with {{ round($topPerformer['rate'], 1) }}% completion rate</span>
                            </div>
                        @endif
                    @endif
                    
                    <div class="flex items-start text-gray-600">
                        <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                        <span class="text-sm">Regular team check-ins can help improve overall productivity</span>
                    </div>
                    
                    <div class="flex items-start text-gray-600">
                        <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                        <span class="text-sm">Monitor overdue tasks to identify potential blockers</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page-scripts')
<script>
// Team page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const assigneeStats = @json($assigneeStats);
    
    // Productivity Chart (Weekly vs Monthly)
    const productivityCtx = document.getElementById('productivityChart').getContext('2d');
    new Chart(productivityCtx, {
        type: 'bar',
        data: {
            labels: ['Weekly', 'Monthly'],
            datasets: [{
                label: 'Completed Tasks',
                data: [{{ $weeklyCompleted }}, {{ $monthlyCompleted }}],
                backgroundColor: ['#3B82F6', '#10B981'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Task Distribution Chart
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    const assigneeNames = Object.keys(assigneeStats);
    const taskCounts = Object.values(assigneeStats).map(stats => stats.total || 0);
    
    new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: assigneeNames.length > 0 ? assigneeNames : ['No Data'],
            datasets: [{
                data: taskCounts.length > 0 ? taskCounts : [1],
                backgroundColor: [
                    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
                    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endsection
