@extends('layouts.app')

@section('page-title', 'ClickUp Settings')

@section('content')
<!-- Notification Area -->
<div id="notification-area" class="fixed top-4 right-4 z-50 space-y-2"></div>

<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-cog text-blue-600 mr-3"></i>
                    ClickUp Settings
                </h1>
                <p class="mt-2 text-gray-600">Configure your ClickUp plugin settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('plugins.manage') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plugin Manager
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
            </div>
        </div>
    @endif

    @if($errors->any())
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div class="text-sm text-red-700">
                    @foreach($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Plugin Settings -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200"
         x-data="clickUpSettings()"
         x-init="init()"
         data-saved-space-id="{{ $config['space_id'] ?? '' }}">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">ClickUp Settings</h3>
                <p class="text-sm text-gray-600">Configure your ClickUp integration settings</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v1.0.0
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                </span>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('plugins.settings.update', 'ClickUp') }}" class="p-6">
        @csrf
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label for="clickup_api_token" class="block text-sm font-medium text-gray-700">
                    API Token <span class="text-red-500">*</span>
                </label>
                <input type="password"
                       id="clickup_api_token"
                       name="api_token"
                       value="{{ old('api_token', $config['api_token'] ?? 'pk_707692_VG30E6E0O5BPKGW98T1TWSZV70AC45XI') }}"
                       required
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">Your ClickUp API token</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_space_id" class="block text-sm font-medium text-gray-700">
                    Space ID <span class="text-red-500">*</span>
                </label>
                
                <!-- Dynamic Space Dropdown -->
                <div x-show="loadingSpaces" class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span class="text-sm text-gray-600">Loading spaces...</span>
                </div>
                
                <div x-show="!loadingSpaces && !spacesError && spaces.length > 0">
                    <select id="clickup_space_id"
                            name="space_id"
                            required
                            x-model="selectedSpaceId"
                            x-init="$nextTick(() => { if (selectedSpaceId) { $el.value = selectedSpaceId; } })"
                            @change="selectedSpaceId = $event.target.value"
                            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Select a space...</option>
                        <template x-for="space in spaces" :key="space.id">
                            <option :value="space.id"
                                    x-text="`${space.name} (${space.team_name})`"></option>
                        </template>
                    </select>
                </div>

                <div x-show="!loadingSpaces && (spacesError || spaces.length === 0)">
                    <div x-show="spacesError" class="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span x-text="spacesError"></span>
                    </div>
                    <input type="text"
                           id="clickup_space_id_fallback"
                           name="space_id"
                           x-model="selectedSpaceId"
                           value="{{ $config['space_id'] ?? '' }}"
                           placeholder="Enter Space ID manually"
                           required
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                
                <p class="text-xs text-gray-500">ClickUp Space ID to manage (required)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_team_id" class="block text-sm font-medium text-gray-700">
                    Team ID
                </label>
                <input type="text"
                       id="clickup_team_id"
                       name="team_id"
                       value="{{ old('team_id', $config['team_id'] ?? '') }}"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">ClickUp team ID for additional filtering (optional)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_sync_interval" class="block text-sm font-medium text-gray-700">
                    Sync Interval (minutes)
                </label>
                <input type="number"
                       id="clickup_sync_interval"
                       name="sync_interval"
                       value="{{ old('sync_interval', $config['sync_interval'] ?? 30) }}"
                       min="1"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">How often to sync task data</p>
            </div>

            <div class="space-y-2 md:col-span-2">
                <label class="flex items-center">
                    <input type="checkbox"
                           name="plugin_enabled"
                           value="1"
                           {{ ($config['plugin_enabled'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Enable Plugin</span>
                </label>
                <p class="text-xs text-gray-500">Enable or disable the ClickUp plugin</p>
            </div>
        </div>

        <div class="mt-6 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <button type="button" 
                        @click="testConnection()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <div id="connection-status" class="text-sm"></div>
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<!-- Page Configuration Section -->
<div class="bg-white shadow rounded-lg mt-8" x-data="pageManagement()">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900">Page Configuration</h3>
                <p class="mt-1 text-sm text-gray-500">
                    Create and manage custom ClickUp dashboard pages with specific lists and folders.
                </p>
            </div>
            <button @click="openCreateModal()"
                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-plus mr-2"></i>
                Create Page
            </button>
        </div>
    </div>

    <!-- Pages List -->
    <div class="p-6">
        <div x-show="loadingPages" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
            <p class="text-gray-500">Loading pages...</p>
        </div>

        <div x-show="!loadingPages && pages.length === 0" class="text-center py-8">
            <i class="fas fa-page text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No custom pages yet</h3>
            <p class="text-gray-500 mb-4">Create your first custom ClickUp dashboard page to get started.</p>
            <button @click="openCreateModal()"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-plus mr-2"></i>
                Create First Page
            </button>
        </div>

        <div x-show="!loadingPages && pages.length > 0" class="space-y-4">
            <template x-for="page in pages" :key="page.id">
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i :class="page.icon" class="text-blue-600 text-xl"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h4 class="text-sm font-medium text-gray-900" x-text="page.name"></h4>
                                <p class="text-sm text-gray-500" x-text="page.description || 'No description'"></p>
                                <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                    <span x-text="`${page.lists_count} lists`"></span>
                                    <span x-text="`${page.folders_count} folders`"></span>
                                    <span x-text="page.page_type"></span>
                                    <span x-show="page.is_default" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Default
                                    </span>
                                    <span x-show="!page.enabled" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                        Disabled
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <a :href="`/clickup/builder/${page.id}`"
                               class="text-purple-600 hover:text-purple-500 p-1"
                               title="Page Builder">
                                <i class="fas fa-paint-brush"></i>
                            </a>
                            <button @click="editPage(page)"
                                    class="text-blue-600 hover:text-blue-500 p-1"
                                    title="Edit Page">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button @click="deletePage(page)"
                                    class="text-red-600 hover:text-red-500 p-1"
                                    title="Delete Page">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Create/Edit Page Modal -->
    <div x-show="showCreateModal || showEditModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                 @click="closeModal()"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="submitForm()">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4"
                                    x-text="showCreateModal ? 'Create New Page' : 'Edit Page'"></h3>

                                <!-- Page Name -->
                                <div class="mb-4">
                                    <label for="page_name" class="block text-sm font-medium text-gray-700">
                                        Page Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="page_name"
                                           x-model="formData.name"
                                           required
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="Enter page name">
                                </div>

                                <!-- Page Description -->
                                <div class="mb-4">
                                    <label for="page_description" class="block text-sm font-medium text-gray-700">
                                        Description
                                    </label>
                                    <textarea id="page_description"
                                              x-model="formData.description"
                                              rows="3"
                                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                              placeholder="Enter page description"></textarea>
                                </div>

                                <!-- Page Type -->
                                <div class="mb-4">
                                    <label for="page_type" class="block text-sm font-medium text-gray-700">
                                        Page Type <span class="text-red-500">*</span>
                                    </label>
                                    <select id="page_type"
                                            x-model="formData.page_type"
                                            required
                                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select page type...</option>
                                        <option value="custom">Custom Dashboard</option>
                                        <option value="tasks">Tasks Overview</option>
                                        <option value="progress">Progress Analytics</option>
                                        <option value="team">Team Performance</option>
                                        <option value="overdue">Overdue Management</option>
                                    </select>
                                </div>

                                <!-- Page Icon -->
                                <div class="mb-4">
                                    <label for="page_icon" class="block text-sm font-medium text-gray-700">
                                        Icon (FontAwesome class)
                                    </label>
                                    <input type="text"
                                           id="page_icon"
                                           x-model="formData.icon"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="e.g., fas fa-chart-bar">
                                </div>

                                <!-- Hierarchical Data Sources Configuration -->
                                <div class="mb-6">
                                    <div class="flex items-center justify-between mb-3">
                                        <label class="block text-sm font-medium text-gray-700">
                                            Data Sources Configuration
                                        </label>
                                        <button type="button"
                                                @click="addDataSource()"
                                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-plus mr-1"></i>
                                            Add Configuration
                                        </button>
                                    </div>

                                    <div class="space-y-4">
                                        <template x-for="(source, index) in formData.data_sources" :key="index">
                                            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                                <div class="flex items-center justify-between mb-3">
                                                    <h4 class="text-sm font-medium text-gray-900">
                                                        Configuration <span x-text="index + 1"></span>
                                                    </h4>
                                                    <button type="button"
                                                            @click="removeDataSource(index)"
                                                            class="text-red-600 hover:text-red-500 p-1">
                                                        <i class="fas fa-trash text-xs"></i>
                                                    </button>
                                                </div>

                                                <!-- Space Selection -->
                                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-700 mb-1">
                                                            Space <span class="text-red-500">*</span>
                                                        </label>
                                                        <select x-model="source.space_id"
                                                                @change="onSpaceChange(index, $event.target.value)"
                                                                :disabled="loadingSpaces || availableSpaces.length === 0"
                                                                class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100">
                                                            <option value="" x-text="loadingSpaces ? 'Loading spaces...' : (availableSpaces.length === 0 ? 'No spaces available' : 'Select space...')"></option>
                                                            <template x-for="space in availableSpaces" :key="space.id">
                                                                <option :value="space.id" x-text="`${space.name} (${space.team_name})`"></option>
                                                            </template>
                                                        </select>

                                                        <!-- Help text for empty spaces -->
                                                        <div x-show="!loadingSpaces && availableSpaces.length === 0"
                                                             class="mt-1 text-xs text-red-600">
                                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                                            No ClickUp spaces found. Please check your API configuration in the settings above.
                                                        </div>
                                                    </div>

                                                    <!-- Folder Selection -->
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-700 mb-1">
                                                            Folder <span class="text-red-500">*</span>
                                                        </label>
                                                        <select x-model="source.folder_id"
                                                                @change="onFolderChange(index, $event.target.value)"
                                                                :disabled="!source.space_id"
                                                                class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100">
                                                            <option value="">Select folder...</option>
                                                            <template x-for="folder in getSpaceFolders(source.space_id)" :key="folder.id">
                                                                <option :value="folder.id" x-text="folder.name"></option>
                                                            </template>
                                                        </select>
                                                    </div>

                                                    <!-- List Selection -->
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-700 mb-1">
                                                            List <span class="text-red-500">*</span>
                                                        </label>
                                                        <select x-model="source.list_id"
                                                                @change="onListChange(index, $event.target.value)"
                                                                :disabled="!source.folder_id"
                                                                class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100">
                                                            <option value="">Select list...</option>
                                                            <template x-for="list in getFolderLists(source.folder_id)" :key="list.id">
                                                                <option :value="list.id" x-text="list.name"></option>
                                                            </template>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Configuration Path Display -->
                                                <div x-show="source.space_name && source.folder_name && source.list_name"
                                                     class="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                                                    <div class="flex items-center text-blue-700">
                                                        <i class="fas fa-sitemap mr-2"></i>
                                                        <span x-text="`${source.space_name} > ${source.folder_name} > ${source.list_name}`"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>

                                        <!-- Empty State -->
                                        <div x-show="formData.data_sources.length === 0"
                                             class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                                            <i class="fas fa-sitemap text-gray-400 text-2xl mb-2"></i>
                                            <p class="text-sm text-gray-500 mb-3">No data sources configured</p>
                                            <button type="button"
                                                    @click="addDataSource()"
                                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                <i class="fas fa-plus mr-2"></i>
                                                Add First Configuration
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Page Settings -->
                                <div class="mb-4">
                                    <div class="flex items-center">
                                        <input type="checkbox"
                                               id="page_enabled"
                                               x-model="formData.enabled"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="page_enabled" class="ml-2 text-sm text-gray-700">
                                            Enable this page
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="flex items-center">
                                        <input type="checkbox"
                                               id="page_default"
                                               x-model="formData.is_default"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="page_default" class="ml-2 text-sm text-gray-700">
                                            Set as default page
                                        </label>
                                    </div>
                                </div>

                                <!-- Error Messages -->
                                <div x-show="formError" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                                    <p class="text-sm text-red-600" x-text="formError"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit"
                                :disabled="submitting"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                            <span x-show="!submitting" x-text="showCreateModal ? 'Create Page' : 'Update Page'"></span>
                            <span x-show="submitting" class="flex items-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                <span x-text="showCreateModal ? 'Creating...' : 'Updating...'"></span>
                            </span>
                        </button>
                        <button type="button"
                                @click="closeModal()"
                                :disabled="submitting"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function clickUpSettings() {
    return {
        loadingSpaces: false,
        spaces: [],
        spacesError: null,
        selectedSpaceId: '',

        init() {
            // Set the saved space ID value from data attribute
            this.setSavedSpaceId();
            this.loadSpaces();
        },

        setSavedSpaceId() {
            const container = document.querySelector('[data-saved-space-id]');
            if (container) {
                const savedId = container.getAttribute('data-saved-space-id') || '';
                this.selectedSpaceId = savedId;

                // If there's no saved space ID, we'll rely on the fallback input
            }
        },
        
        async loadSpaces() {
            this.loadingSpaces = true;
            this.spacesError = null;

            // Preserve the selected space ID during loading
            const savedSpaceId = this.selectedSpaceId;

            try {
                const response = await fetch('/api/plugins/clickup/spaces');
                const data = await response.json();

                if (data.success) {
                    this.spaces = data.spaces || [];

                    // Restore the selected space ID after spaces are loaded
                    this.selectedSpaceId = savedSpaceId;

                    // Double-check that the space ID is properly set after a short delay
                    setTimeout(() => {
                        if (savedSpaceId && this.selectedSpaceId !== savedSpaceId) {
                            this.selectedSpaceId = savedSpaceId;
                        }

                        // Force update the select element to ensure proper display
                        const selectElement = document.getElementById('clickup_space_id');
                        if (selectElement && savedSpaceId) {
                            selectElement.value = savedSpaceId;
                        }

                        // Also update fallback input if it's visible
                        const fallbackElement = document.getElementById('clickup_space_id_fallback');
                        if (fallbackElement && savedSpaceId) {
                            fallbackElement.value = savedSpaceId;
                        }
                    }, 100);

                    if (this.spaces.length === 0) {
                        this.spacesError = 'No spaces found. Please check your API token and permissions.';
                    }
                } else {
                    this.spacesError = data.message || 'Failed to load spaces from ClickUp API. You can enter the Space ID manually below.';
                }
            } catch (error) {
                console.error('Error loading ClickUp spaces:', error);
                this.spacesError = 'Unable to connect to ClickUp API. You can enter the Space ID manually below.';
            } finally {
                this.loadingSpaces = false;

                // Ensure the selected space ID is preserved even if there was an error
                if (savedSpaceId && !this.selectedSpaceId) {
                    this.selectedSpaceId = savedSpaceId;
                }

                // If there was an error and we have a saved space ID, make sure the fallback input shows it
                if (this.spacesError && savedSpaceId) {
                    setTimeout(() => {
                        const fallbackElement = document.getElementById('clickup_space_id_fallback');
                        if (fallbackElement) {
                            fallbackElement.value = savedSpaceId;
                        }
                    }, 50);
                }
            }
        },
        
        async testConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-600"></i> Testing...';
            
            try {
                const response = await fetch('/api/plugins/clickup/test-connection');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '<i class="fas fa-check-circle text-green-600"></i> Connection successful';
                } else {
                    statusDiv.innerHTML = '<i class="fas fa-exclamation-circle text-red-600"></i> ' + data.message;
                }
            } catch (error) {
                statusDiv.innerHTML = '<i class="fas fa-times-circle text-red-600"></i> Connection error';
            }
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
    }
}

// Page Management Component
function pageManagement() {
    return {
        pages: [],
        loadingPages: true,
        showCreateModal: false,
        showEditModal: false,
        currentPage: null,
        availableSpaces: [],
        spaceHierarchies: {}, // Cache for space hierarchies
        loadingSpaces: false,
        submitting: false,
        formError: '',
        formData: {
            name: '',
            description: '',
            page_type: '',
            icon: 'fas fa-page',
            enabled: true,
            is_default: false,
            data_sources: []
        },

        init() {
            this.loadPages();
            this.loadAvailableSpaces();
        },

        async loadPages() {
            this.loadingPages = true;
            try {
                const response = await fetch('/api/plugins/clickup/pages');
                const data = await response.json();

                if (data.success) {
                    this.pages = data.pages || [];
                } else {
                    console.error('Failed to load pages:', data.message);
                }
            } catch (error) {
                console.error('Error loading pages:', error);
            } finally {
                this.loadingPages = false;
            }
        },

        async loadAvailableSpaces() {
            this.loadingSpaces = true;
            try {
                console.log('Loading available spaces...');
                const response = await fetch('/api/plugins/clickup/available-spaces');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Available spaces response:', data);

                if (data.success) {
                    this.availableSpaces = data.data || [];
                    console.log('Loaded spaces:', this.availableSpaces);

                    if (this.availableSpaces.length === 0) {
                        this.showErrorMessage('No ClickUp spaces found. Please check your API configuration.');
                    }
                } else {
                    this.showErrorMessage(data.message || 'Failed to load ClickUp spaces');
                    console.error('API returned error:', data.message);
                }
            } catch (error) {
                console.error('Error loading available spaces:', error);
                this.showErrorMessage('Failed to load ClickUp spaces. Please check your connection and API configuration.');
            } finally {
                this.loadingSpaces = false;
            }
        },

        async loadSpaceHierarchy(spaceId) {
            if (this.spaceHierarchies[spaceId]) {
                return this.spaceHierarchies[spaceId];
            }

            try {
                const response = await fetch(`/api/plugins/clickup/space-hierarchy/${spaceId}`);
                const data = await response.json();

                if (data.success) {
                    this.spaceHierarchies[spaceId] = data.data || [];
                    return this.spaceHierarchies[spaceId];
                }
            } catch (error) {
                console.error('Error loading space hierarchy:', error);
            }

            return [];
        },

        openCreateModal() {
            this.resetForm();
            this.showCreateModal = true;
        },

        editPage(page) {
            this.currentPage = { ...page };
            this.populateForm(page);
            this.showEditModal = true;
        },

        closeModal() {
            this.showCreateModal = false;
            this.showEditModal = false;
            this.resetForm();
            this.formError = '';
        },

        resetForm() {
            this.formData = {
                name: '',
                description: '',
                page_type: '',
                icon: 'fas fa-page',
                enabled: true,
                is_default: false,
                data_sources: []
            };
        },

        addDataSource() {
            this.formData.data_sources.push({
                space_id: '',
                space_name: '',
                folder_id: '',
                folder_name: '',
                list_id: '',
                list_name: ''
            });
        },

        removeDataSource(index) {
            this.formData.data_sources.splice(index, 1);
        },

        async onSpaceChange(index, spaceId) {
            const source = this.formData.data_sources[index];
            const space = this.availableSpaces.find(s => s.id === spaceId);

            if (space) {
                source.space_id = spaceId;
                source.space_name = `${space.name} (${space.team_name})`;

                // Reset dependent fields
                source.folder_id = '';
                source.folder_name = '';
                source.list_id = '';
                source.list_name = '';

                // Load hierarchy for this space
                await this.loadSpaceHierarchy(spaceId);
            }
        },

        onFolderChange(index, folderId) {
            const source = this.formData.data_sources[index];
            const folders = this.getSpaceFolders(source.space_id);
            const folder = folders.find(f => f.id === folderId);

            if (folder) {
                source.folder_id = folderId;
                source.folder_name = folder.name;

                // Reset dependent fields
                source.list_id = '';
                source.list_name = '';
            }
        },

        onListChange(index, listId) {
            const source = this.formData.data_sources[index];
            const lists = this.getFolderLists(source.folder_id);
            const list = lists.find(l => l.id === listId);

            if (list) {
                source.list_id = listId;
                source.list_name = list.name;
            }
        },

        getSpaceFolders(spaceId) {
            return this.spaceHierarchies[spaceId] || [];
        },

        getFolderLists(folderId) {
            for (const spaceId in this.spaceHierarchies) {
                const folders = this.spaceHierarchies[spaceId];
                const folder = folders.find(f => f.id === folderId);
                if (folder) {
                    return folder.lists || [];
                }
            }
            return [];
        },

        populateForm(page) {
            this.formData = {
                name: page.name || '',
                description: page.description || '',
                page_type: page.page_type || '',
                icon: page.icon || 'fas fa-page',
                enabled: page.enabled !== undefined ? page.enabled : true,
                is_default: page.is_default !== undefined ? page.is_default : false,
                data_sources: page.data_sources ? [...page.data_sources] : []
            };

            // Load space hierarchies for existing data sources
            this.loadExistingDataSourceHierarchies();
        },

        async loadExistingDataSourceHierarchies() {
            const spaceIds = [...new Set(this.formData.data_sources.map(source => source.space_id))];

            for (const spaceId of spaceIds) {
                if (spaceId) {
                    await this.loadSpaceHierarchy(spaceId);
                }
            }
        },

        async submitForm() {
            this.submitting = true;
            this.formError = '';

            try {
                // Validate form
                if (!this.formData.name.trim()) {
                    throw new Error('Page name is required');
                }
                if (!this.formData.page_type) {
                    throw new Error('Page type is required');
                }

                // Validate data sources
                if (this.formData.data_sources.length === 0) {
                    throw new Error('At least one data source configuration is required');
                }

                for (let i = 0; i < this.formData.data_sources.length; i++) {
                    const source = this.formData.data_sources[i];
                    if (!source.space_id) {
                        throw new Error(`Configuration ${i + 1}: Space selection is required`);
                    }
                    if (!source.folder_id) {
                        throw new Error(`Configuration ${i + 1}: Folder selection is required`);
                    }
                    if (!source.list_id) {
                        throw new Error(`Configuration ${i + 1}: List selection is required`);
                    }
                }

                const url = this.showCreateModal
                    ? '/api/plugins/clickup/pages'
                    : `/api/plugins/clickup/pages/${this.currentPage.id}`;

                const method = this.showCreateModal ? 'POST' : 'PUT';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.formData)
                });

                const data = await response.json();

                if (data.success) {
                    this.closeModal();
                    this.loadPages(); // Reload pages list

                    // Show success message
                    const message = this.showCreateModal ? 'Page created successfully!' : 'Page updated successfully!';
                    this.showSuccessMessage(message);

                    // Refresh sidebar navigation
                    this.refreshSidebar();
                } else {
                    this.formError = data.message || 'An error occurred while saving the page';
                }
            } catch (error) {
                console.error('Form submission error:', error);
                this.formError = error.message || 'An unexpected error occurred';
            } finally {
                this.submitting = false;
            }
        },

        showSuccessMessage(message) {
            this.showNotification(message, 'success');
        },

        showErrorMessage(message) {
            this.showNotification(message, 'error');
        },

        showNotification(message, type = 'success') {
            const notificationArea = document.getElementById('notification-area');
            const notification = document.createElement('div');

            const baseClasses = 'px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out';
            const typeClasses = type === 'success'
                ? 'bg-green-500 text-white'
                : 'bg-red-500 text-white';

            notification.className = `${baseClasses} ${typeClasses} translate-x-full opacity-0`;

            // Add icon and message
            const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            notificationArea.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // Animate out and remove
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        },

        async deletePage(page) {
            if (!confirm(`Are you sure you want to delete the page "${page.name}"?`)) {
                return;
            }

            try {
                const response = await fetch(`/api/plugins/clickup/pages/${page.id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.loadPages(); // Reload pages
                    this.showSuccessMessage('Page deleted successfully');
                    this.refreshSidebar(); // Update sidebar navigation
                } else {
                    this.showErrorMessage('Failed to delete page: ' + data.message);
                }
            } catch (error) {
                console.error('Error deleting page:', error);
                this.showErrorMessage('Error deleting page');
            }
        },

        refreshSidebar() {
            // Refresh the entire page to update sidebar navigation
            // This ensures the sidebar reflects the latest page changes
            setTimeout(() => {
                window.location.reload();
            }, 1500); // Give time for the success message to be seen
        }
    }
}
</script>
</div>
@endsection
