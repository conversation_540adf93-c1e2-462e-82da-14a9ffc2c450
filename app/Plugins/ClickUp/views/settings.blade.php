@extends('layouts.app')

@section('page-title', 'ClickUp Settings')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-cog text-blue-600 mr-3"></i>
                    ClickUp Settings
                </h1>
                <p class="mt-2 text-gray-600">Configure your ClickUp plugin settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{{ route('plugins.manage') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plugin Manager
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
            </div>
        </div>
    @endif

    @if($errors->any())
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div class="text-sm text-red-700">
                    @foreach($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Plugin Settings -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200"
         x-data="clickUpSettings()"
         x-init="init()"
         data-saved-space-id="{{ $config['space_id'] ?? '' }}">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">ClickUp Settings</h3>
                <p class="text-sm text-gray-600">Configure your ClickUp integration settings</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v1.0.0
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                </span>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('plugins.settings.update', 'ClickUp') }}" class="p-6">
        @csrf
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label for="clickup_api_token" class="block text-sm font-medium text-gray-700">
                    API Token <span class="text-red-500">*</span>
                </label>
                <input type="password"
                       id="clickup_api_token"
                       name="api_token"
                       value="{{ old('api_token', $config['api_token'] ?? 'pk_707692_VG30E6E0O5BPKGW98T1TWSZV70AC45XI') }}"
                       required
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">Your ClickUp API token</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_space_id" class="block text-sm font-medium text-gray-700">
                    Space ID <span class="text-red-500">*</span>
                </label>
                
                <!-- Dynamic Space Dropdown -->
                <div x-show="loadingSpaces" class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span class="text-sm text-gray-600">Loading spaces...</span>
                </div>
                
                <div x-show="!loadingSpaces && !spacesError && spaces.length > 0">
                    <select id="clickup_space_id"
                            name="space_id"
                            required
                            x-model="selectedSpaceId"
                            x-init="$nextTick(() => { if (selectedSpaceId) { $el.value = selectedSpaceId; } })"
                            @change="selectedSpaceId = $event.target.value"
                            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Select a space...</option>
                        <template x-for="space in spaces" :key="space.id">
                            <option :value="space.id"
                                    x-text="`${space.name} (${space.team_name})`"></option>
                        </template>
                    </select>
                </div>

                <div x-show="!loadingSpaces && (spacesError || spaces.length === 0)">
                    <div x-show="spacesError" class="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span x-text="spacesError"></span>
                    </div>
                    <input type="text"
                           id="clickup_space_id_fallback"
                           name="space_id"
                           x-model="selectedSpaceId"
                           value="{{ $config['space_id'] ?? '' }}"
                           placeholder="Enter Space ID manually"
                           required
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                
                <p class="text-xs text-gray-500">ClickUp Space ID to manage (required)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_team_id" class="block text-sm font-medium text-gray-700">
                    Team ID
                </label>
                <input type="text"
                       id="clickup_team_id"
                       name="team_id"
                       value="{{ old('team_id', $config['team_id'] ?? '') }}"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">ClickUp team ID for additional filtering (optional)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_sync_interval" class="block text-sm font-medium text-gray-700">
                    Sync Interval (minutes)
                </label>
                <input type="number"
                       id="clickup_sync_interval"
                       name="sync_interval"
                       value="{{ old('sync_interval', $config['sync_interval'] ?? 30) }}"
                       min="1"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">How often to sync task data</p>
            </div>

            <div class="space-y-2 md:col-span-2">
                <label class="flex items-center">
                    <input type="checkbox"
                           name="plugin_enabled"
                           value="1"
                           {{ ($config['plugin_enabled'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Enable Plugin</span>
                </label>
                <p class="text-xs text-gray-500">Enable or disable the ClickUp plugin</p>
            </div>
        </div>

        <div class="mt-6 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <button type="button" 
                        @click="testConnection()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <div id="connection-status" class="text-sm"></div>
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<!-- Page Configuration Section -->
<div class="bg-white shadow rounded-lg mt-8" x-data="pageManagement()">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900">Page Configuration</h3>
                <p class="mt-1 text-sm text-gray-500">
                    Create and manage custom ClickUp dashboard pages with specific lists and folders.
                </p>
            </div>
            <button @click="showCreateModal = true"
                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-plus mr-2"></i>
                Create Page
            </button>
        </div>
    </div>

    <!-- Pages List -->
    <div class="p-6">
        <div x-show="loadingPages" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
            <p class="text-gray-500">Loading pages...</p>
        </div>

        <div x-show="!loadingPages && pages.length === 0" class="text-center py-8">
            <i class="fas fa-page text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No custom pages yet</h3>
            <p class="text-gray-500 mb-4">Create your first custom ClickUp dashboard page to get started.</p>
            <button @click="showCreateModal = true"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <i class="fas fa-plus mr-2"></i>
                Create First Page
            </button>
        </div>

        <div x-show="!loadingPages && pages.length > 0" class="space-y-4">
            <template x-for="page in pages" :key="page.id">
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i :class="page.icon" class="text-blue-600 text-xl"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h4 class="text-sm font-medium text-gray-900" x-text="page.name"></h4>
                                <p class="text-sm text-gray-500" x-text="page.description || 'No description'"></p>
                                <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                    <span x-text="`${page.lists_count} lists`"></span>
                                    <span x-text="`${page.folders_count} folders`"></span>
                                    <span x-text="page.page_type"></span>
                                    <span x-show="page.is_default" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Default
                                    </span>
                                    <span x-show="!page.enabled" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                        Disabled
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button @click="editPage(page)"
                                    class="text-blue-600 hover:text-blue-500 p-1">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button @click="deletePage(page)"
                                    class="text-red-600 hover:text-red-500 p-1">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function clickUpSettings() {
    return {
        loadingSpaces: false,
        spaces: [],
        spacesError: null,
        selectedSpaceId: '',

        init() {
            // Set the saved space ID value from data attribute
            this.setSavedSpaceId();
            this.loadSpaces();
        },

        setSavedSpaceId() {
            const container = document.querySelector('[data-saved-space-id]');
            if (container) {
                const savedId = container.getAttribute('data-saved-space-id') || '';
                this.selectedSpaceId = savedId;

                // If there's no saved space ID, we'll rely on the fallback input
            }
        },
        
        async loadSpaces() {
            this.loadingSpaces = true;
            this.spacesError = null;

            // Preserve the selected space ID during loading
            const savedSpaceId = this.selectedSpaceId;

            try {
                const response = await fetch('/api/plugins/clickup/spaces');
                const data = await response.json();

                if (data.success) {
                    this.spaces = data.spaces || [];

                    // Restore the selected space ID after spaces are loaded
                    this.selectedSpaceId = savedSpaceId;

                    // Double-check that the space ID is properly set after a short delay
                    setTimeout(() => {
                        if (savedSpaceId && this.selectedSpaceId !== savedSpaceId) {
                            this.selectedSpaceId = savedSpaceId;
                        }

                        // Force update the select element to ensure proper display
                        const selectElement = document.getElementById('clickup_space_id');
                        if (selectElement && savedSpaceId) {
                            selectElement.value = savedSpaceId;
                        }

                        // Also update fallback input if it's visible
                        const fallbackElement = document.getElementById('clickup_space_id_fallback');
                        if (fallbackElement && savedSpaceId) {
                            fallbackElement.value = savedSpaceId;
                        }
                    }, 100);

                    if (this.spaces.length === 0) {
                        this.spacesError = 'No spaces found. Please check your API token and permissions.';
                    }
                } else {
                    this.spacesError = data.message || 'Failed to load spaces from ClickUp API. You can enter the Space ID manually below.';
                }
            } catch (error) {
                console.error('Error loading ClickUp spaces:', error);
                this.spacesError = 'Unable to connect to ClickUp API. You can enter the Space ID manually below.';
            } finally {
                this.loadingSpaces = false;

                // Ensure the selected space ID is preserved even if there was an error
                if (savedSpaceId && !this.selectedSpaceId) {
                    this.selectedSpaceId = savedSpaceId;
                }

                // If there was an error and we have a saved space ID, make sure the fallback input shows it
                if (this.spacesError && savedSpaceId) {
                    setTimeout(() => {
                        const fallbackElement = document.getElementById('clickup_space_id_fallback');
                        if (fallbackElement) {
                            fallbackElement.value = savedSpaceId;
                        }
                    }, 50);
                }
            }
        },
        
        async testConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-600"></i> Testing...';
            
            try {
                const response = await fetch('/api/plugins/clickup/test-connection');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '<i class="fas fa-check-circle text-green-600"></i> Connection successful';
                } else {
                    statusDiv.innerHTML = '<i class="fas fa-exclamation-circle text-red-600"></i> ' + data.message;
                }
            } catch (error) {
                statusDiv.innerHTML = '<i class="fas fa-times-circle text-red-600"></i> Connection error';
            }
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
    }
}

// Page Management Component
function pageManagement() {
    return {
        pages: [],
        loadingPages: true,
        showCreateModal: false,
        showEditModal: false,
        currentPage: null,
        lists: [],
        folders: [],
        loadingLists: false,
        loadingFolders: false,

        init() {
            this.loadPages();
            this.loadLists();
            this.loadFolders();
        },

        async loadPages() {
            this.loadingPages = true;
            try {
                const response = await fetch('/api/plugins/clickup/pages');
                const data = await response.json();

                if (data.success) {
                    this.pages = data.pages || [];
                } else {
                    console.error('Failed to load pages:', data.message);
                }
            } catch (error) {
                console.error('Error loading pages:', error);
            } finally {
                this.loadingPages = false;
            }
        },

        async loadLists() {
            this.loadingLists = true;
            try {
                const response = await fetch('/api/plugins/clickup/lists');
                const data = await response.json();

                if (data.success) {
                    this.lists = data.data || [];
                }
            } catch (error) {
                console.error('Error loading lists:', error);
            } finally {
                this.loadingLists = false;
            }
        },

        async loadFolders() {
            this.loadingFolders = true;
            try {
                const response = await fetch('/api/plugins/clickup/folders');
                const data = await response.json();

                if (data.success) {
                    this.folders = data.data || [];
                }
            } catch (error) {
                console.error('Error loading folders:', error);
            } finally {
                this.loadingFolders = false;
            }
        },

        editPage(page) {
            this.currentPage = { ...page };
            this.showEditModal = true;
        },

        async deletePage(page) {
            if (!confirm(`Are you sure you want to delete the page "${page.name}"?`)) {
                return;
            }

            try {
                const response = await fetch(`/api/plugins/clickup/pages/${page.id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.loadPages(); // Reload pages
                    alert('Page deleted successfully');
                } else {
                    alert('Failed to delete page: ' + data.message);
                }
            } catch (error) {
                console.error('Error deleting page:', error);
                alert('Error deleting page');
            }
        }
    }
}
</script>
</div>
@endsection
