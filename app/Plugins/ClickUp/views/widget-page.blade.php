@extends('clickup::layout')

@section('page-title', $page->name)

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="{{ $page->icon ?: 'fas fa-chart-bar' }} text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-2xl font-bold text-gray-900">{{ $page->name }}</h1>
                    @if($page->description)
                        <p class="text-gray-600">{{ $page->description }}</p>
                    @endif
                </div>
            </div>
            <div class="flex items-center space-x-3">
                @if(!$page->is_published)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-eye-slash mr-1"></i>
                        Draft
                    </span>
                @endif
                <a href="/clickup/builder/{{ $page->id }}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-paint-brush mr-2"></i>
                    Edit Page
                </a>
            </div>
        </div>
    </div>

    <!-- Widget Grid -->
    <div class="grid grid-cols-12 gap-6" style="grid-auto-rows: minmax(200px, auto);">
        @foreach($widgets as $widget)
            @if($widget['type'] !== 'error')
                <div class="col-span-{{ $widget['config']['position']['width'] ?? 6 }} 
                           row-span-{{ $widget['config']['position']['height'] ?? 2 }}"
                     style="grid-column-start: {{ ($widget['config']['position']['x'] ?? 0) + 1 }}; 
                            grid-row-start: {{ ($widget['config']['position']['y'] ?? 0) + 1 }};">
                    
                    @if($widget['type'] === 'table')
                        @include('clickup::widgets.table', ['widget' => $widget])
                    @elseif($widget['type'] === 'number_card')
                        @include('clickup::widgets.number-card', ['widget' => $widget])
                    @elseif($widget['type'] === 'progress_card')
                        @include('clickup::widgets.progress-card', ['widget' => $widget])
                    @elseif($widget['type'] === 'status_chart')
                        @include('clickup::widgets.status-chart', ['widget' => $widget])
                    @elseif($widget['type'] === 'timeline_card')
                        @include('clickup::widgets.timeline-card', ['widget' => $widget])
                    @elseif($widget['type'] === 'assignee_chart')
                        @include('clickup::widgets.assignee-chart', ['widget' => $widget])
                    @endif
                </div>
            @else
                <!-- Error Widget -->
                <div class="col-span-6 row-span-1">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">{{ $widget['title'] }}</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    {{ $widget['data']['message'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
        
        @if(empty($widgets))
            <!-- Empty State -->
            <div class="col-span-12 flex items-center justify-center h-64 border-2 border-dashed border-gray-300 rounded-lg">
                <div class="text-center">
                    <i class="fas fa-paint-brush text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Widgets Added</h3>
                    <p class="text-gray-500 mb-4">Use the page builder to add widgets and create your custom dashboard</p>
                    <a href="/clickup/builder/{{ $page->id }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-paint-brush mr-2"></i>
                        Open Page Builder
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Real-time Updates -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh widgets every 5 minutes
    setInterval(function() {
        // Refresh page data
        window.location.reload();
    }, 300000); // 5 minutes
});
</script>
@endsection
