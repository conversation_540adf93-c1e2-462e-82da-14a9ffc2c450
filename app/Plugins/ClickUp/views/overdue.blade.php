@extends('clickup::layout')

@section('page-content')
<!-- Overdue Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    @php
        $totalOverdue = count($overdueTasks);
        $urgentOverdue = collect($overdueTasks)->where('priority.priority', 'urgent')->count();
        $highOverdue = collect($overdueTasks)->where('priority.priority', 'high')->count();
        $avgDaysOverdue = 0;
        
        if ($totalOverdue > 0) {
            $totalDaysOverdue = collect($overdueTasks)->sum(function($task) {
                if (isset($task['due_date'])) {
                    $dueDate = strtotime($task['due_date']);
                    $now = time();
                    return max(0, ($now - $dueDate) / (24 * 60 * 60));
                }
                return 0;
            });
            $avgDaysOverdue = round($totalDaysOverdue / $totalOverdue, 1);
        }
    @endphp
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Overdue</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $totalOverdue }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-fire text-red-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Urgent Priority</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $urgentOverdue }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-arrow-up text-orange-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">High Priority</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $highOverdue }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar-times text-gray-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg Days Overdue</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $avgDaysOverdue }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Priority Alert -->
@if($urgentOverdue > 0 || $highOverdue > 0)
    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-0.5"></i>
            <div>
                <h3 class="text-sm font-medium text-red-800">High Priority Overdue Tasks</h3>
                <p class="mt-1 text-sm text-red-700">
                    You have {{ $urgentOverdue + $highOverdue }} high or urgent priority tasks that are overdue. 
                    These require immediate attention to prevent project delays.
                </p>
            </div>
        </div>
    </div>
@endif

<!-- Overdue Tasks by Priority -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Priority Distribution Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Overdue Tasks by Priority</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="priorityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Days Overdue Distribution -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Days Overdue Distribution</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="daysOverdueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Tasks List -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Overdue Tasks ({{ count($overdueTasks) }})</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">Sort by:</span>
                <select id="sortSelect" class="text-sm border-gray-300 rounded-md">
                    <option value="priority">Priority</option>
                    <option value="dueDate">Due Date</option>
                    <option value="daysOverdue">Days Overdue</option>
                </select>
            </div>
        </div>
    </div>
    <div class="overflow-hidden">
        @if(count($overdueTasks) > 0)
            <ul class="divide-y divide-gray-200" id="overdueTasksList">
                @foreach($overdueTasks as $task)
                    @php
                        $daysOverdue = 0;
                        if (isset($task['due_date'])) {
                            $dueDate = strtotime($task['due_date']);
                            $now = time();
                            $daysOverdue = max(0, round(($now - $dueDate) / (24 * 60 * 60)));
                        }
                        
                        $priorityColor = match($task['priority']['priority'] ?? 'normal') {
                            'urgent' => 'bg-red-100 text-red-800',
                            'high' => 'bg-orange-100 text-orange-800',
                            'normal' => 'bg-yellow-100 text-yellow-800',
                            default => 'bg-gray-100 text-gray-800'
                        };
                        
                        $urgencyClass = $daysOverdue > 7 ? 'border-l-4 border-red-500' : ($daysOverdue > 3 ? 'border-l-4 border-orange-500' : 'border-l-4 border-yellow-500');
                    @endphp
                    <li class="px-6 py-4 hover:bg-gray-50 {{ $urgencyClass }}" 
                        data-priority="{{ $task['priority']['priority'] ?? 'normal' }}"
                        data-due-date="{{ $task['due_date'] ?? '' }}"
                        data-days-overdue="{{ $daysOverdue }}">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-600"></i>
                                </div>
                                <div class="ml-4 min-w-0 flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">
                                            {{ $task['name'] ?? 'Untitled Task' }}
                                        </h4>
                                        @if(isset($task['priority']) && $task['priority']['priority'])
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $priorityColor }}">
                                                {{ ucfirst($task['priority']['priority']) }}
                                            </span>
                                        @endif
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {{ $daysOverdue }} day{{ $daysOverdue !== 1 ? 's' : '' }} overdue
                                        </span>
                                    </div>
                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                        @if(isset($task['due_date']))
                                            <span>Due: {{ date('M j, Y', strtotime($task['due_date'])) }}</span>
                                        @endif
                                        @if(isset($task['assignees']) && count($task['assignees']) > 0)
                                            <span class="mx-2">•</span>
                                            <span>Assigned to: {{ $task['assignees'][0]['username'] ?? 'Unknown' }}</span>
                                        @endif
                                        @if(isset($task['list']['name']))
                                            <span class="mx-2">•</span>
                                            <span>List: {{ $task['list']['name'] }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 flex items-center space-x-2">
                                @if(isset($task['url']))
                                    <a href="{{ $task['url'] }}" target="_blank" 
                                       class="text-blue-600 hover:text-blue-500 p-2">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                @endif
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">
                                        @if($daysOverdue > 7)
                                            <span class="text-red-600">Critical</span>
                                        @elseif($daysOverdue > 3)
                                            <span class="text-orange-600">High Risk</span>
                                        @else
                                            <span class="text-yellow-600">Attention Needed</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        @else
            <div class="px-6 py-12 text-center">
                <i class="fas fa-check-circle text-green-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No overdue tasks!</h3>
                <p class="text-gray-500">
                    @if(isset($error))
                        There was an error loading overdue tasks. Please check your ClickUp configuration.
                    @else
                        Great job! All tasks are on track or completed.
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>

<!-- Action Recommendations -->
@if(count($overdueTasks) > 0)
    <div class="mt-6 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recommended Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Immediate Actions</h4>
                    <div class="space-y-3">
                        @if($urgentOverdue > 0)
                            <div class="flex items-start text-red-600">
                                <i class="fas fa-fire mr-2 mt-0.5"></i>
                                <span class="text-sm">Address {{ $urgentOverdue }} urgent overdue task{{ $urgentOverdue !== 1 ? 's' : '' }} immediately</span>
                            </div>
                        @endif
                        
                        @if($avgDaysOverdue > 5)
                            <div class="flex items-start text-orange-600">
                                <i class="fas fa-clock mr-2 mt-0.5"></i>
                                <span class="text-sm">Review task deadlines - average {{ $avgDaysOverdue }} days overdue</span>
                            </div>
                        @endif
                        
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-users mr-2 mt-0.5"></i>
                            <span class="text-sm">Contact assignees for status updates on overdue tasks</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Prevention Strategies</h4>
                    <div class="space-y-3">
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-calendar-check mr-2 mt-0.5"></i>
                            <span class="text-sm">Set up automated reminders before due dates</span>
                        </div>
                        
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-balance-scale mr-2 mt-0.5"></i>
                            <span class="text-sm">Review workload distribution among team members</span>
                        </div>
                        
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-chart-line mr-2 mt-0.5"></i>
                            <span class="text-sm">Monitor progress regularly to catch delays early</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
@endsection

@section('page-scripts')
<script>
// Overdue page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const overdueTasks = @json($overdueTasks);
    
    // Priority Distribution Chart
    const priorityCtx = document.getElementById('priorityChart').getContext('2d');
    const priorityCounts = {
        urgent: {{ $urgentOverdue }},
        high: {{ $highOverdue }},
        normal: {{ $totalOverdue - $urgentOverdue - $highOverdue }},
    };
    
    new Chart(priorityCtx, {
        type: 'doughnut',
        data: {
            labels: ['Urgent', 'High', 'Normal'],
            datasets: [{
                data: [priorityCounts.urgent, priorityCounts.high, priorityCounts.normal],
                backgroundColor: ['#EF4444', '#F59E0B', '#6B7280'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Days Overdue Distribution Chart
    const daysOverdueCtx = document.getElementById('daysOverdueChart').getContext('2d');
    const daysOverdueData = overdueTasks.map(task => {
        if (task.due_date) {
            const dueDate = new Date(task.due_date);
            const now = new Date();
            return Math.max(0, Math.round((now - dueDate) / (24 * 60 * 60 * 1000)));
        }
        return 0;
    });
    
    // Group by ranges
    const ranges = {
        '1-3 days': daysOverdueData.filter(d => d >= 1 && d <= 3).length,
        '4-7 days': daysOverdueData.filter(d => d >= 4 && d <= 7).length,
        '1-2 weeks': daysOverdueData.filter(d => d >= 8 && d <= 14).length,
        '2+ weeks': daysOverdueData.filter(d => d > 14).length
    };
    
    new Chart(daysOverdueCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(ranges),
            datasets: [{
                label: 'Tasks',
                data: Object.values(ranges),
                backgroundColor: ['#F59E0B', '#EF4444', '#DC2626', '#991B1B'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Sort functionality
    const sortSelect = document.getElementById('sortSelect');
    const tasksList = document.getElementById('overdueTasksList');
    
    if (sortSelect && tasksList) {
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            const tasks = Array.from(tasksList.children);
            
            tasks.sort((a, b) => {
                switch(sortBy) {
                    case 'priority':
                        const priorityOrder = { urgent: 3, high: 2, normal: 1 };
                        const aPriority = priorityOrder[a.dataset.priority] || 0;
                        const bPriority = priorityOrder[b.dataset.priority] || 0;
                        return bPriority - aPriority;
                    
                    case 'dueDate':
                        const aDate = new Date(a.dataset.dueDate || 0);
                        const bDate = new Date(b.dataset.dueDate || 0);
                        return aDate - bDate;
                    
                    case 'daysOverdue':
                        const aDays = parseInt(a.dataset.daysOverdue) || 0;
                        const bDays = parseInt(b.dataset.daysOverdue) || 0;
                        return bDays - aDays;
                    
                    default:
                        return 0;
                }
            });
            
            // Re-append sorted tasks
            tasks.forEach(task => tasksList.appendChild(task));
        });
    }
});
</script>
@endsection
