@extends('clickup::layout')

@section('page-title', 'ClickUp Overview')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">ClickUp Overview</h1>
                <p class="text-gray-600 mt-1">Manage your ClickUp dashboard pages and view analytics</p>
            </div>
            <a href="{{ route('clickup.settings') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-cog mr-2"></i>
                Manage Pages
            </a>
        </div>
    </div>

    @if($pages->count() > 0)
        <!-- Pages Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($pages as $page)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <!-- Page Header -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="{{ $page->icon ?: 'fas fa-chart-bar' }} text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $page->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ ucfirst($page->page_type) }} Page</p>
                                </div>
                            </div>
                            @if($page->getDataSources())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ count($page->getDataSources()) }} sources
                                </span>
                            @endif
                        </div>

                        <!-- Page Description -->
                        @if($page->description)
                            <p class="text-gray-600 text-sm mb-4">{{ $page->description }}</p>
                        @endif

                        <!-- Data Sources Preview -->
                        @if($page->getDataSources())
                            <div class="mb-4">
                                <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Data Sources</h4>
                                <div class="space-y-1">
                                    @foreach(array_slice($page->getDataSources(), 0, 2) as $source)
                                        <div class="text-xs text-gray-600 bg-gray-50 rounded px-2 py-1">
                                            <i class="fas fa-sitemap mr-1"></i>
                                            {{ $source['space_name'] }} > {{ $source['folder_name'] }} > {{ $source['list_name'] }}
                                        </div>
                                    @endforeach
                                    @if(count($page->getDataSources()) > 2)
                                        <div class="text-xs text-gray-500">
                                            +{{ count($page->getDataSources()) - 2 }} more sources
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div class="text-xs text-gray-500">
                                Updated {{ $page->updated_at->diffForHumans() }}
                            </div>
                            <a href="{{ route('clickup.page', $page->slug) }}" 
                               class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View Page
                                <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Statistics</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $pages->count() }}</div>
                        <div class="text-sm text-gray-500">Total Pages</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $pages->where('enabled', true)->count() }}</div>
                        <div class="text-sm text-gray-500">Active Pages</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ $pages->sum(function($page) { return count($page->getDataSources()); }) }}</div>
                        <div class="text-sm text-gray-500">Data Sources</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">{{ $pages->groupBy('page_type')->count() }}</div>
                        <div class="text-sm text-gray-500">Page Types</div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-chart-bar text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Pages Created Yet</h3>
            <p class="text-gray-600 mb-6">Create your first ClickUp dashboard page to get started with data visualization.</p>
            <a href="{{ route('clickup.settings') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-2"></i>
                Create Your First Page
            </a>
        </div>
    @endif
</div>
@endsection
