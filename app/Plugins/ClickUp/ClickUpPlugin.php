<?php

namespace App\Plugins\ClickUp;

use App\Plugins\BasePlugin;

class ClickUpPlugin extends BasePlugin
{
    protected string $name = 'ClickUp';
    protected string $description = 'Task progress tracking and project management integration';
    protected string $version = '1.0.0';
    protected bool $enabled = true; // Enabled for implementation

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'clickup_metrics',
                'title' => 'ClickUp Metrics',
                'component' => 'ClickUpMetrics',
                'size' => 'large',
                'order' => 1,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'task_progress',
                'title' => 'Task Progress',
                'component' => 'TaskProgress',
                'size' => 'medium',
                'order' => 2,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'overdue_tasks',
                'title' => 'Overdue Tasks',
                'component' => 'OverdueTasks',
                'size' => 'small',
                'order' => 3,
            ],
        ];
    }

    public function registerSidebarItems(): array
    {
        $sidebarItems = [];

        // Add static menu items first
        $sidebarItems[] = [
            'title' => 'Overview',
            'icon' => 'fas fa-tachometer-alt',
            'url' => '/clickup/overview',
        ];

        // Add dynamic pages from database
        try {
            $pages = \App\Plugins\ClickUp\Models\ClickUpPage::where('enabled', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();

            foreach ($pages as $page) {
                $sidebarItems[] = [
                    'title' => $page->name,
                    'icon' => $page->icon ?: 'fas fa-chart-bar',
                    'url' => '/clickup/pages/' . $page->slug,
                    'badge' => $page->getDataSources() ? count($page->getDataSources()) : null,
                ];
            }
        } catch (\Exception $e) {
            // If database is not ready or pages table doesn't exist, continue with static items
            \Log::warning('Could not load ClickUp pages for sidebar: ' . $e->getMessage());
        }

        // Add settings at the end
        $sidebarItems[] = [
            'title' => 'Settings',
            'icon' => 'fas fa-cog',
            'url' => '/clickup/settings',
        ];

        return $sidebarItems;
    }

    public function getConfigSchema(): array
    {
        return [
            'api_token' => [
                'type' => 'string',
                'label' => 'API Token',
                'description' => 'Your ClickUp API token',
                'required' => true,
            ],
            'space_id' => [
                'type' => 'string',
                'label' => 'Space ID',
                'description' => 'ClickUp Space ID to sync with',
                'required' => false,
            ],
            'team_id' => [
                'type' => 'string',
                'label' => 'Team ID',
                'description' => 'ClickUp Team ID',
                'required' => false,
            ],
            'sync_interval' => [
                'type' => 'number',
                'label' => 'Sync Interval (minutes)',
                'description' => 'How often to sync data from ClickUp',
                'default' => 30,
                'required' => false,
            ],
        ];
    }

    public function boot(): void
    {
        // Call parent boot to register views
        parent::boot();

        // Plugin boot logic
        // Register any additional services or bindings here
        $this->registerClickUpViews();
    }

    /**
     * Register ClickUp-specific view namespaces
     */
    protected function registerClickUpViews(): void
    {
        $viewsPath = $this->getPluginPath() . '/views';

        if (file_exists($viewsPath)) {
            // Register with 'clickup' namespace (lowercase)
            view()->addNamespace('clickup', $viewsPath);

            // Also register with 'ClickUp' namespace for backward compatibility
            view()->addNamespace('ClickUp', $viewsPath);

            // Add to view finder locations
            view()->getFinder()->addLocation($viewsPath);
        }
    }
}
