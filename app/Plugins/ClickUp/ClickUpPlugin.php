<?php

namespace App\Plugins\ClickUp;

use App\Plugins\BasePlugin;

class ClickUpPlugin extends BasePlugin
{
    protected string $name = 'ClickUp';
    protected string $description = 'Task progress tracking and project management integration';
    protected string $version = '1.0.0';
    protected bool $enabled = true; // Enabled for implementation

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'task_progress',
                'title' => 'Task Progress',
                'component' => 'ClickUpTaskProgressWidget',
                'size' => 'medium',
                'order' => 10,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'overdue_tasks',
                'title' => 'Overdue Tasks',
                'component' => 'ClickUpOverdueTasksWidget',
                'size' => 'medium',
                'order' => 11,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'team_productivity',
                'title' => 'Team Productivity',
                'component' => 'ClickUpProductivityWidget',
                'size' => 'large',
                'order' => 12,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_token' => [
                'type' => 'string',
                'label' => 'API Token',
                'description' => 'Your ClickUp API token',
                'required' => true,
                'sensitive' => true,
                'default' => 'pk_707692_VG30E6E0O5BPKGW98T1TWSZV70AC45XI',
            ],
            'space_id' => [
                'type' => 'string',
                'label' => 'Space ID',
                'description' => 'ClickUp Space ID to manage (required)',
                'required' => true,
            ],
            'team_id' => [
                'type' => 'string',
                'label' => 'Team ID',
                'description' => 'ClickUp team ID for additional filtering (optional)',
                'required' => false,
            ],
            'sync_interval' => [
                'type' => 'integer',
                'label' => 'Sync Interval (minutes)',
                'description' => 'How often to sync task data',
                'required' => false,
                'default' => 30,
            ],
        ];
    }
}
