<?php

namespace App\Plugins\TestPlugin;

use App\Plugins\BasePlugin;
use App\Plugins\PluginInterface;

class TestPluginPlugin extends BasePlugin implements PluginInterface
{
    protected $name = 'TestPlugin';
    protected $description = 'A test plugin for demonstration purposes';
    protected $version = '1.0.0';
    protected $enabled = true;

    public function boot(): void
    {
        // Plugin boot logic
    }

    public function registerRoutes(): void
    {
        // No routes for test plugin
    }

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'test_widget',
                'title' => 'Test Widget',
                'component' => 'TestWidget',
                'size' => 'small',
                'order' => 100,
            ],
        ];
    }

    public function registerSidebarItems(): array
    {
        return [
            [
                'title' => 'Test Plugin',
                'icon' => 'fas fa-flask',
                'url' => '/test-plugin',
            ],
        ];
    }
}
