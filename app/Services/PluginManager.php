<?php

namespace App\Services;

use App\Contracts\PluginInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;

class PluginManager
{
    protected Collection $plugins;
    protected Collection $widgets;

    public function __construct()
    {
        $this->plugins = collect();
        $this->widgets = collect();
    }

    /**
     * Discover and register all plugins
     */
    public function discoverPlugins(): void
    {
        $pluginsPath = app_path('Plugins');
        
        if (!File::exists($pluginsPath)) {
            File::makeDirectory($pluginsPath, 0755, true);
            return;
        }

        $pluginDirectories = File::directories($pluginsPath);

        foreach ($pluginDirectories as $pluginDir) {
            $this->loadPlugin($pluginDir);
        }
    }

    /**
     * Load a specific plugin
     */
    protected function loadPlugin(string $pluginDir): void
    {
        $pluginName = basename($pluginDir);
        $pluginClass = "App\\Plugins\\{$pluginName}\\{$pluginName}Plugin";

        if (class_exists($pluginClass)) {
            $plugin = new $pluginClass();
            
            if ($plugin instanceof PluginInterface && $plugin->isEnabled()) {
                $this->registerPlugin($plugin);
            }
        }
    }

    /**
     * Register a plugin
     */
    public function registerPlugin(PluginInterface $plugin): void
    {
        $this->plugins->put($plugin->getName(), $plugin);
        
        // Boot the plugin
        $plugin->boot();
        
        // Register routes
        $plugin->registerRoutes();
        
        // Register widgets
        $widgets = $plugin->registerWidgets();
        foreach ($widgets as $widget) {
            $this->widgets->push($widget);
        }
    }

    /**
     * Get all registered plugins
     */
    public function getPlugins(): Collection
    {
        return $this->plugins;
    }

    /**
     * Get a specific plugin
     */
    public function getPlugin(string $name): ?PluginInterface
    {
        return $this->plugins->get($name);
    }

    /**
     * Get all registered widgets
     */
    public function getWidgets(): Collection
    {
        return $this->widgets;
    }

    /**
     * Get widgets for a specific plugin
     */
    public function getPluginWidgets(string $pluginName): Collection
    {
        return $this->widgets->filter(function ($widget) use ($pluginName) {
            return $widget['plugin'] === $pluginName;
        });
    }

    /**
     * Check if a plugin is registered
     */
    public function hasPlugin(string $name): bool
    {
        return $this->plugins->has($name);
    }

    /**
     * Get plugin configuration from plugin-specific .env file
     */
    public function getPluginConfig(string $pluginName): array
    {
        $plugin = $this->getPlugin($pluginName);
        if (!$plugin) {
            return [];
        }

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($plugin);
        $method = $reflection->getMethod('loadPluginConfig');
        $method->setAccessible(true);

        return $method->invoke($plugin);
    }

    /**
     * Update plugin configuration in plugin-specific .env file
     */
    public function updatePluginConfig(string $pluginName, array $config): bool
    {
        $plugin = $this->getPlugin($pluginName);
        if (!$plugin) {
            return false;
        }

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($plugin);
        $method = $reflection->getMethod('savePluginConfig');
        $method->setAccessible(true);

        return $method->invoke($plugin, $config);
    }
}
