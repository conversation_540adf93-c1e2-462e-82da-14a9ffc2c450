<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class PluginImportExport
{
    protected string $pluginsPath;
    protected string $tempPath;

    public function __construct()
    {
        $this->pluginsPath = app_path('Plugins');
        $this->tempPath = storage_path('app/temp/plugins');
        
        // Ensure temp directory exists
        if (!File::exists($this->tempPath)) {
            File::makeDirectory($this->tempPath, 0755, true);
        }
    }

    /**
     * Export a plugin as a ZIP file
     */
    public function exportPlugin(string $pluginName): array
    {
        $pluginPath = $this->pluginsPath . '/' . $pluginName;
        
        if (!File::exists($pluginPath)) {
            return [
                'success' => false,
                'message' => "Plugin '{$pluginName}' not found."
            ];
        }

        try {
            $zipFileName = strtolower($pluginName) . '_plugin_' . date('Y-m-d_H-i-s') . '.zip';
            $zipPath = $this->tempPath . '/' . $zipFileName;

            $zip = new ZipArchive();
            if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
                return [
                    'success' => false,
                    'message' => 'Failed to create ZIP file.'
                ];
            }

            // Add all plugin files to ZIP
            $this->addDirectoryToZip($zip, $pluginPath, $pluginName);
            
            // Add plugin metadata
            $metadata = $this->generatePluginMetadata($pluginName);
            $zip->addFromString('plugin.json', json_encode($metadata, JSON_PRETTY_PRINT));

            $zip->close();

            return [
                'success' => true,
                'file_path' => $zipPath,
                'file_name' => $zipFileName,
                'download_url' => route('plugins.download', ['file' => $zipFileName])
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import a plugin from a ZIP file
     */
    public function importPlugin(string $zipFilePath): array
    {
        if (!File::exists($zipFilePath)) {
            \Log::error('Plugin import: ZIP file not found', [
                'path' => $zipFilePath,
                'absolute_path' => realpath($zipFilePath),
                'directory_exists' => is_dir(dirname($zipFilePath)),
                'directory_contents' => is_dir(dirname($zipFilePath)) ? scandir(dirname($zipFilePath)) : 'N/A'
            ]);
            return [
                'success' => false,
                'message' => "ZIP file not found at path: {$zipFilePath}"
            ];
        }

        if (!is_readable($zipFilePath)) {
            return [
                'success' => false,
                'message' => 'ZIP file is not readable. Please check file permissions.'
            ];
        }

        try {
            $zip = new ZipArchive();
            if ($zip->open($zipFilePath) !== TRUE) {
                return [
                    'success' => false,
                    'message' => 'Failed to open ZIP file.'
                ];
            }

            // Extract and validate plugin metadata
            $metadataContent = $zip->getFromName('plugin.json');
            if ($metadataContent === false) {
                $zip->close();
                return [
                    'success' => false,
                    'message' => 'Invalid plugin ZIP: missing plugin.json metadata.'
                ];
            }

            $metadata = json_decode($metadataContent, true);
            if (!$metadata || !isset($metadata['name'])) {
                $zip->close();
                return [
                    'success' => false,
                    'message' => 'Invalid plugin metadata.'
                ];
            }

            $pluginName = $metadata['name'];
            $targetPath = $this->pluginsPath . '/' . $pluginName;

            // Check if plugin already exists
            if (File::exists($targetPath)) {
                return [
                    'success' => false,
                    'message' => "Plugin '{$pluginName}' already exists. Please remove it first or use update functionality."
                ];
            }

            // Extract plugin files
            $extractPath = $this->tempPath . '/extract_' . uniqid();
            if (!$zip->extractTo($extractPath)) {
                $zip->close();
                return [
                    'success' => false,
                    'message' => 'Failed to extract plugin files.'
                ];
            }

            $zip->close();

            // Move extracted files to plugins directory
            $extractedPluginPath = $extractPath . '/' . $pluginName;
            if (File::exists($extractedPluginPath)) {
                File::moveDirectory($extractedPluginPath, $targetPath);
            } else {
                // If no subdirectory, move all contents
                File::moveDirectory($extractPath, $targetPath);
            }

            // Clean up temp extraction directory
            File::deleteDirectory($extractPath);

            return [
                'success' => true,
                'plugin_name' => $pluginName,
                'message' => "Plugin '{$pluginName}' imported successfully."
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Add directory contents to ZIP recursively
     */
    protected function addDirectoryToZip(ZipArchive $zip, string $dirPath, string $pluginName, string $localPath = ''): void
    {
        $files = File::allFiles($dirPath);
        
        foreach ($files as $file) {
            $relativePath = $file->getRelativePathname();
            $zipPath = $pluginName . '/' . $localPath . $relativePath;
            $zip->addFile($file->getRealPath(), $zipPath);
        }
    }

    /**
     * Generate plugin metadata
     */
    protected function generatePluginMetadata(string $pluginName): array
    {
        $pluginClass = "App\\Plugins\\{$pluginName}\\{$pluginName}Plugin";
        
        $metadata = [
            'name' => $pluginName,
            'exported_at' => now()->toISOString(),
            'version' => '1.0.0',
            'description' => 'Exported plugin',
            'requires' => [
                'php' => '>=8.1',
                'laravel' => '>=11.0'
            ]
        ];

        // Try to get metadata from plugin class
        if (class_exists($pluginClass)) {
            try {
                $plugin = new $pluginClass();
                $metadata['version'] = $plugin->getVersion();
                $metadata['description'] = $plugin->getDescription();
            } catch (\Exception $e) {
                // Use defaults if plugin instantiation fails
            }
        }

        return $metadata;
    }

    /**
     * Clean up old temporary files
     */
    public function cleanupTempFiles(int $olderThanHours = 24): void
    {
        $files = File::files($this->tempPath);
        $cutoffTime = now()->subHours($olderThanHours);

        foreach ($files as $file) {
            if ($file->getMTime() < $cutoffTime->timestamp) {
                File::delete($file->getRealPath());
            }
        }
    }

    /**
     * Get temporary file path for download
     */
    public function getTempFilePath(string $fileName): ?string
    {
        $filePath = $this->tempPath . '/' . $fileName;
        return File::exists($filePath) ? $filePath : null;
    }
}
