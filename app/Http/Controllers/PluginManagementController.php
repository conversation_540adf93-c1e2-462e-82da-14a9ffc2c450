<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use App\Services\PluginImportExport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\File;

class PluginManagementController extends Controller
{
    protected PluginManager $pluginManager;
    protected PluginImportExport $importExport;

    public function __construct(PluginManager $pluginManager, PluginImportExport $importExport)
    {
        $this->pluginManager = $pluginManager;
        $this->importExport = $importExport;
    }

    /**
     * Display the plugin management page
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getPlugins();
        $pluginData = [];

        foreach ($plugins as $plugin) {
            $pluginName = $plugin->getName();
            $pluginData[] = [
                'name' => $pluginName,
                'description' => $plugin->getDescription(),
                'version' => $plugin->getVersion(),
                'enabled' => $plugin->isEnabled(),
                'config' => $this->pluginManager->getPluginConfig($pluginName),
                'has_settings' => $this->hasSettingsView($pluginName),
            ];
        }

        return view('plugins.manage', compact('pluginData'));
    }

    /**
     * Export a plugin as ZIP
     */
    public function export(string $pluginName): JsonResponse
    {
        $result = $this->importExport->exportPlugin($pluginName);
        return response()->json($result);
    }

    /**
     * Import a plugin from ZIP
     */
    public function import(Request $request): RedirectResponse
    {
        try {
            $request->validate([
                'plugin_zip' => 'required|file|mimes:zip|max:10240', // 10MB max
            ]);

            $uploadedFile = $request->file('plugin_zip');

            if (!$uploadedFile) {
                return back()->withErrors(['import' => 'No file was uploaded.']);
            }

            if (!$uploadedFile->isValid()) {
                return back()->withErrors(['import' => 'The uploaded file is invalid.']);
            }

            // Ensure the temp/plugins directory exists
            $tempDir = storage_path('app/temp/plugins');
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Generate unique filename
            $fileName = uniqid('plugin_import_') . '.zip';
            $tempPath = $uploadedFile->storeAs('temp/plugins', $fileName);
            $fullPath = storage_path('app/' . $tempPath);

            // Debug logging
            \Log::info('Plugin import attempt', [
                'original_name' => $uploadedFile->getClientOriginalName(),
                'temp_path' => $tempPath,
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath),
                'file_size' => file_exists($fullPath) ? filesize($fullPath) : 'N/A',
                'is_readable' => file_exists($fullPath) ? is_readable($fullPath) : false
            ]);

            if (!file_exists($fullPath)) {
                \Log::error('Plugin import: File not found after upload', [
                    'expected_path' => $fullPath,
                    'temp_path' => $tempPath,
                    'storage_path' => storage_path('app'),
                    'temp_dir_exists' => is_dir($tempDir),
                    'temp_dir_writable' => is_writable($tempDir)
                ]);
                return back()->withErrors(['import' => 'Failed to save uploaded file. Please check storage permissions.']);
            }

            if (!is_readable($fullPath)) {
                return back()->withErrors(['import' => 'Uploaded file is not readable. Please check file permissions.']);
            }

            $result = $this->importExport->importPlugin($fullPath);

            // Clean up uploaded file (with error handling)
            try {
                if (file_exists($fullPath)) {
                    File::delete($fullPath);
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to clean up temporary file: ' . $e->getMessage());
            }

            if ($result['success']) {
                return back()->with('success', $result['message']);
            } else {
                return back()->withErrors(['import' => $result['message']]);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors());
        } catch (\Exception $e) {
            \Log::error('Plugin import error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['import' => 'Import failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Download exported plugin
     */
    public function download(string $fileName)
    {
        $filePath = $this->importExport->getTempFilePath($fileName);

        if (!$filePath) {
            abort(404, 'File not found');
        }

        return response()->download($filePath, $fileName)->deleteFileAfterSend();
    }

    /**
     * Toggle plugin activation
     */
    public function toggleActivation(string $pluginName): JsonResponse
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            return response()->json([
                'success' => false,
                'message' => 'Plugin not found'
            ], 404);
        }

        $isCurrentlyEnabled = $plugin->isEnabled();

        if ($isCurrentlyEnabled) {
            $success = $this->pluginManager->disablePlugin($pluginName);
            $newState = false;
            $action = 'disabled';
        } else {
            $success = $this->pluginManager->enablePlugin($pluginName);
            $newState = true;
            $action = 'enabled';
        }

        return response()->json([
            'success' => $success,
            'enabled' => $newState,
            'message' => $success
                ? "Plugin {$pluginName} {$action} successfully"
                : "Failed to {$action} plugin {$pluginName}"
        ]);
    }

    /**
     * Enable a plugin
     */
    public function enablePlugin(string $pluginName): JsonResponse
    {
        $success = $this->pluginManager->enablePlugin($pluginName);

        return response()->json([
            'success' => $success,
            'enabled' => true,
            'message' => $success
                ? "Plugin {$pluginName} enabled successfully"
                : "Failed to enable plugin {$pluginName}"
        ]);
    }

    /**
     * Disable a plugin
     */
    public function disablePlugin(string $pluginName): JsonResponse
    {
        $success = $this->pluginManager->disablePlugin($pluginName);

        return response()->json([
            'success' => $success,
            'enabled' => false,
            'message' => $success
                ? "Plugin {$pluginName} disabled successfully"
                : "Failed to disable plugin {$pluginName}"
        ]);
    }

    /**
     * Uninstall a plugin
     */
    public function uninstallPlugin(string $pluginName): JsonResponse
    {
        $result = $this->pluginManager->uninstallPlugin($pluginName);

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Show plugin settings
     */
    public function settings(string $pluginName): View
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            abort(404, 'Plugin not found');
        }

        $config = $this->pluginManager->getPluginConfig($pluginName);

        // Check if plugin has custom settings view
        if ($this->hasSettingsView($pluginName)) {
            // Register the plugin's view path and render directly
            $this->registerPluginViewPath($pluginName);
            return view('settings', compact('config', 'plugin', 'pluginName'));
        }

        // Fallback to generic settings view
        $settingsView = null;
        return view('plugins.settings', compact('plugin', 'config', 'settingsView', 'pluginName'));
    }

    /**
     * Update plugin settings
     */
    public function updateSettings(Request $request, string $pluginName): RedirectResponse
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            return back()->withErrors(['plugin' => 'Plugin not found']);
        }

        $config = $request->except(['_token']);

        // Convert checkbox values
        if (isset($config['plugin_enabled'])) {
            $config['plugin_enabled'] = true;
        } else {
            $config['plugin_enabled'] = false;
        }

        $success = $this->pluginManager->updatePluginConfig($pluginName, $config);

        if ($success) {
            return back()->with('success', "Settings for {$pluginName} updated successfully");
        } else {
            return back()->withErrors(['update' => 'Failed to update plugin settings']);
        }
    }

    /**
     * Check if plugin has settings view
     */
    protected function hasSettingsView(string $pluginName): bool
    {
        $viewPath = app_path("Plugins/{$pluginName}/views/settings.blade.php");
        return File::exists($viewPath);
    }

    /**
     * Register plugin view path with Laravel's view system
     */
    protected function registerPluginViewPath(string $pluginName): void
    {
        $pluginViewPath = app_path("Plugins/{$pluginName}/views");

        if (File::exists($pluginViewPath)) {
            // Add the plugin's view directory to Laravel's view finder
            view()->getFinder()->addLocation($pluginViewPath);

            // Also register it as a namespace for better organization
            view()->addNamespace("plugins.{$pluginName}", $pluginViewPath);
        }
    }

    /**
     * Get plugin settings view content (legacy method - kept for fallback)
     */
    protected function getPluginSettingsView(string $pluginName): ?string
    {
        $viewPath = app_path("Plugins/{$pluginName}/views/settings.blade.php");

        if (File::exists($viewPath)) {
            return File::get($viewPath);
        }

        return null;
    }

    /**
     * Run migrations for a specific plugin
     */
    public function runMigrations(string $pluginName): JsonResponse
    {
        $success = $this->pluginManager->runPluginMigrations($pluginName);

        return response()->json([
            'success' => $success,
            'message' => $success
                ? "Migrations for {$pluginName} completed successfully"
                : "Failed to run migrations for {$pluginName}"
        ]);
    }

    /**
     * Run migrations for all plugins
     */
    public function runAllMigrations(): JsonResponse
    {
        $results = $this->pluginManager->runAllPluginMigrations();

        $successCount = count(array_filter($results));
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount === $totalCount,
            'results' => $results,
            'message' => "Completed migrations for {$successCount}/{$totalCount} plugins"
        ]);
    }

    /**
     * Create a new migration for a plugin
     */
    public function createMigration(Request $request, string $pluginName): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|regex:/^[a-z_]+$/',
        ]);

        $migrationName = $request->input('name');
        $filePath = $this->pluginManager->createPluginMigration($pluginName, $migrationName);

        if ($filePath) {
            return response()->json([
                'success' => true,
                'file_path' => $filePath,
                'message' => "Migration {$migrationName} created successfully for {$pluginName}"
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => "Failed to create migration for {$pluginName}"
            ], 400);
        }
    }
}
