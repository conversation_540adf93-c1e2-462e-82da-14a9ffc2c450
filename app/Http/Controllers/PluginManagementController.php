<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use App\Services\PluginImportExport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\File;

class PluginManagementController extends Controller
{
    protected PluginManager $pluginManager;
    protected PluginImportExport $importExport;

    public function __construct(PluginManager $pluginManager, PluginImportExport $importExport)
    {
        $this->pluginManager = $pluginManager;
        $this->importExport = $importExport;
    }

    /**
     * Display the plugin management page
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getPlugins();
        $pluginData = [];

        foreach ($plugins as $plugin) {
            $pluginName = $plugin->getName();
            $pluginData[] = [
                'name' => $pluginName,
                'description' => $plugin->getDescription(),
                'version' => $plugin->getVersion(),
                'enabled' => $plugin->isEnabled(),
                'config' => $this->pluginManager->getPluginConfig($pluginName),
                'has_settings' => $this->hasSettingsView($pluginName),
            ];
        }

        return view('plugins.manage', compact('pluginData'));
    }

    /**
     * Export a plugin as ZIP
     */
    public function export(string $pluginName): JsonResponse
    {
        $result = $this->importExport->exportPlugin($pluginName);
        return response()->json($result);
    }

    /**
     * Import a plugin from ZIP
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'plugin_zip' => 'required|file|mimes:zip|max:10240', // 10MB max
        ]);

        $uploadedFile = $request->file('plugin_zip');
        $tempPath = $uploadedFile->storeAs('temp/plugins', uniqid() . '.zip');
        $fullPath = storage_path('app/' . $tempPath);

        $result = $this->importExport->importPlugin($fullPath);

        // Clean up uploaded file
        File::delete($fullPath);

        if ($result['success']) {
            return back()->with('success', $result['message']);
        } else {
            return back()->withErrors(['import' => $result['message']]);
        }
    }

    /**
     * Download exported plugin
     */
    public function download(string $fileName)
    {
        $filePath = $this->importExport->getTempFilePath($fileName);

        if (!$filePath) {
            abort(404, 'File not found');
        }

        return response()->download($filePath, $fileName)->deleteFileAfterSend();
    }

    /**
     * Toggle plugin activation
     */
    public function toggleActivation(string $pluginName): JsonResponse
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            return response()->json([
                'success' => false,
                'message' => 'Plugin not found'
            ], 404);
        }

        $config = $this->pluginManager->getPluginConfig($pluginName);
        $config['plugin_enabled'] = !($config['plugin_enabled'] ?? true);

        $success = $this->pluginManager->updatePluginConfig($pluginName, $config);

        return response()->json([
            'success' => $success,
            'enabled' => $config['plugin_enabled'],
            'message' => $success
                ? "Plugin {$pluginName} " . ($config['plugin_enabled'] ? 'enabled' : 'disabled') . " successfully"
                : 'Failed to update plugin status'
        ]);
    }

    /**
     * Show plugin settings
     */
    public function settings(string $pluginName): View
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            abort(404, 'Plugin not found');
        }

        $config = $this->pluginManager->getPluginConfig($pluginName);

        // Check if plugin has custom settings view
        if ($this->hasSettingsView($pluginName)) {
            // Register the plugin's view path and render directly
            $this->registerPluginViewPath($pluginName);
            return view("plugins.{$pluginName}.settings", compact('config', 'plugin', 'pluginName'));
        }

        // Fallback to generic settings view
        $settingsView = null;
        return view('plugins.settings', compact('plugin', 'config', 'settingsView', 'pluginName'));
    }

    /**
     * Update plugin settings
     */
    public function updateSettings(Request $request, string $pluginName): RedirectResponse
    {
        $plugin = $this->pluginManager->getPlugin($pluginName);

        if (!$plugin) {
            return back()->withErrors(['plugin' => 'Plugin not found']);
        }

        $config = $request->except(['_token']);

        // Convert checkbox values
        if (isset($config['plugin_enabled'])) {
            $config['plugin_enabled'] = true;
        } else {
            $config['plugin_enabled'] = false;
        }

        $success = $this->pluginManager->updatePluginConfig($pluginName, $config);

        if ($success) {
            return back()->with('success', "Settings for {$pluginName} updated successfully");
        } else {
            return back()->withErrors(['update' => 'Failed to update plugin settings']);
        }
    }

    /**
     * Check if plugin has settings view
     */
    protected function hasSettingsView(string $pluginName): bool
    {
        $viewPath = app_path("Plugins/{$pluginName}/views/settings.blade.php");
        return File::exists($viewPath);
    }

    /**
     * Register plugin view path with Laravel's view system
     */
    protected function registerPluginViewPath(string $pluginName): void
    {
        $pluginViewPath = app_path("Plugins/{$pluginName}/views");

        if (File::exists($pluginViewPath)) {
            // Add the plugin's view directory to Laravel's view finder
            view()->getFinder()->addLocation($pluginViewPath);

            // Also register it as a namespace for better organization
            view()->addNamespace("plugins.{$pluginName}", $pluginViewPath);
        }
    }

    /**
     * Get plugin settings view content (legacy method - kept for fallback)
     */
    protected function getPluginSettingsView(string $pluginName): ?string
    {
        $viewPath = app_path("Plugins/{$pluginName}/views/settings.blade.php");

        if (File::exists($viewPath)) {
            return File::get($viewPath);
        }

        return null;
    }
}
