<?php

namespace App\Providers;

use App\Services\PluginManager;
use Illuminate\Support\ServiceProvider;

class PluginServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PluginManager::class, fn() => new PluginManager());

        // Register plugin services
        $this->app->singleton(\App\Plugins\TaqnyatAdmin\Services\TaqnyatAdminService::class);
        $this->app->singleton(\App\Plugins\ClickUp\Services\ClickUpService::class);
        $this->app->singleton(\App\Plugins\ClickUp\Services\WidgetService::class);
        $this->app->singleton(\App\Services\PluginImportExport::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register plugin views early
        $this->registerPluginViews();

        $pluginManager = $this->app->make(PluginManager::class);
        $pluginManager->discoverPlugins();
    }

    /**
     * Register all plugin view namespaces
     */
    protected function registerPluginViews(): void
    {
        $pluginsPath = app_path('Plugins');

        if (!file_exists($pluginsPath)) {
            return;
        }

        $pluginDirectories = glob($pluginsPath . '/*', GLOB_ONLYDIR);

        foreach ($pluginDirectories as $pluginDir) {
            $pluginName = basename($pluginDir);
            $viewsPath = $pluginDir . '/views';

            if (file_exists($viewsPath)) {
                // Register with lowercase plugin name
                view()->addNamespace(strtolower($pluginName), $viewsPath);

                // Also register with original case for backward compatibility
                view()->addNamespace($pluginName, $viewsPath);
            }
        }
    }
}
