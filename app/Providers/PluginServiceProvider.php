<?php

namespace App\Providers;

use App\Services\PluginManager;
use Illuminate\Support\ServiceProvider;

class PluginServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PluginManager::class, fn() => new PluginManager());

        // Register plugin services
        $this->app->singleton(\App\Plugins\TaqnyatAdmin\Services\TaqnyatAdminService::class);
        $this->app->singleton(\App\Plugins\ClickUp\Services\ClickUpService::class);
        $this->app->singleton(\App\Plugins\ClickUp\Services\WidgetService::class);
        $this->app->singleton(\App\Services\PluginImportExport::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $pluginManager = $this->app->make(PluginManager::class);
        $pluginManager->discoverPlugins();
    }
}
