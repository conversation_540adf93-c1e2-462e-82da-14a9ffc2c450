<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    protected PluginManager $pluginManager;

    public function __construct(PluginManager $pluginManager)
    {
        $this->pluginManager = $pluginManager;
    }

    /**
     * Display the dashboard
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getPlugins();
        $widgets = $this->pluginManager->getWidgets();

        return view('dashboard.index', compact('plugins', 'widgets'));
    }

    /**
     * Get dashboard data via API
     */
    public function getData(Request $request)
    {
        $plugins = $this->pluginManager->getPlugins();
        $widgets = $this->pluginManager->getWidgets();

        return response()->json([
            'plugins' => $plugins->map(function ($plugin) {
                return [
                    'name' => $plugin->getName(),
                    'description' => $plugin->getDescription(),
                    'version' => $plugin->getVersion(),
                    'enabled' => $plugin->isEnabled(),
                ];
            }),
            'widgets' => $widgets,
        ]);
    }
}
