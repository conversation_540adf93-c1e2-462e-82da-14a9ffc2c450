<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class SettingsController extends Controller
{
    protected PluginManager $pluginManager;

    public function __construct(PluginManager $pluginManager)
    {
        $this->pluginManager = $pluginManager;
    }

    /**
     * Display the settings page
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getPlugins();
        $pluginConfigs = [];

        foreach ($plugins as $plugin) {
            $pluginName = strtolower($plugin->getName());
            $pluginConfigs[$pluginName] = [
                'plugin' => $plugin,
                'config' => $this->pluginManager->getPluginConfig($pluginName),
                'schema' => $plugin->getConfigSchema(),
            ];
        }

        return view('settings.index', compact('pluginConfigs'));
    }

    /**
     * Update plugin settings
     */
    public function update(Request $request): RedirectResponse
    {
        $pluginName = $request->input('plugin');
        $config = $request->input('config', []);

        // Validate the plugin exists
        if (!$this->pluginManager->hasPlugin($pluginName)) {
            return back()->withErrors(['plugin' => 'Plugin not found.']);
        }

        // Get the plugin and its schema for validation
        $plugin = $this->pluginManager->getPlugin($pluginName);
        $schema = $plugin->getConfigSchema();

        // Basic validation based on schema
        $validatedConfig = [];
        foreach ($schema as $key => $field) {
            $value = $config[$key] ?? null;

            if ($field['required'] && empty($value)) {
                return back()->withErrors([$key => "The {$field['label']} field is required."]);
            }

            if (!empty($value)) {
                // Type validation
                switch ($field['type']) {
                    case 'integer':
                        $validatedConfig[$key] = (int) $value;
                        break;
                    case 'boolean':
                        $validatedConfig[$key] = (bool) $value;
                        break;
                    default:
                        $validatedConfig[$key] = $value;
                }
            } elseif (isset($field['default'])) {
                $validatedConfig[$key] = $field['default'];
            }
        }

        // Update the plugin configuration
        $this->pluginManager->updatePluginConfig(strtolower($pluginName), $validatedConfig);

        return back()->with('success', "Settings for {$pluginName} have been updated successfully.");
    }
}
