<?php

namespace App\Plugins\Etimad;

use App\Plugins\BasePlugin;

class EtimadPlugin extends BasePlugin
{
    protected string $name = 'Etimad';
    protected string $description = 'Government tenders monitoring and opportunity tracking';
    protected string $version = '1.0.0';
    protected bool $enabled = false; // Disabled by default as placeholder

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'tender_opportunities',
                'title' => 'Tender Opportunities',
                'component' => 'EtimadTenderWidget',
                'size' => 'large',
                'order' => 40,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'tender_alerts',
                'title' => 'Tender Alerts',
                'component' => 'EtimadAlertsWidget',
                'size' => 'medium',
                'order' => 41,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_endpoint' => [
                'type' => 'string',
                'label' => 'API Endpoint',
                'description' => 'Etimad API endpoint URL',
                'required' => true,
                'default' => 'https://api.etimad.sa',
            ],
            'api_key' => [
                'type' => 'string',
                'label' => 'API Key',
                'description' => 'Etimad API authentication key',
                'required' => true,
                'sensitive' => true,
            ],
            'categories' => [
                'type' => 'string',
                'label' => 'Tender Categories',
                'description' => 'Comma-separated list of tender categories to monitor',
                'required' => false,
                'default' => 'IT,Software,Technology',
            ],
            'min_value' => [
                'type' => 'integer',
                'label' => 'Minimum Tender Value',
                'description' => 'Minimum tender value to track (in SAR)',
                'required' => false,
                'default' => 100000,
            ],
            'alert_email' => [
                'type' => 'string',
                'label' => 'Alert Email',
                'description' => 'Email address for tender alerts',
                'required' => false,
            ],
        ];
    }
}
