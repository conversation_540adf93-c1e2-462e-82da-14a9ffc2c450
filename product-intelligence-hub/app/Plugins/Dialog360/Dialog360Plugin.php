<?php

namespace App\Plugins\Dialog360;

use App\Plugins\BasePlugin;

class Dialog360Plugin extends BasePlugin
{
    protected string $name = 'Dialog360';
    protected string $description = 'WhatsApp API usage analytics and customer communication insights';
    protected string $version = '1.0.0';
    protected bool $enabled = false; // Disabled by default as placeholder

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'whatsapp_usage',
                'title' => 'WhatsApp Usage',
                'component' => 'Dialog360UsageWidget',
                'size' => 'medium',
                'order' => 30,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'message_analytics',
                'title' => 'Message Analytics',
                'component' => 'Dialog360AnalyticsWidget',
                'size' => 'large',
                'order' => 31,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_key' => [
                'type' => 'string',
                'label' => 'API Key',
                'description' => '360dialog API key',
                'required' => true,
                'sensitive' => true,
            ],
            'channel_id' => [
                'type' => 'string',
                'label' => 'Channel ID',
                'description' => 'WhatsApp Business Channel ID',
                'required' => true,
            ],
            'webhook_url' => [
                'type' => 'string',
                'label' => 'Webhook URL',
                'description' => 'URL for receiving webhook notifications',
                'required' => false,
            ],
            'track_metrics' => [
                'type' => 'boolean',
                'label' => 'Track Metrics',
                'description' => 'Enable detailed message metrics tracking',
                'required' => false,
                'default' => true,
            ],
        ];
    }
}
