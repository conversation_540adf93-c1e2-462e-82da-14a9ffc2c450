<?php

use App\Plugins\TaqnyatAdmin\Controllers\TaqnyatAdminController;
use Illuminate\Support\Facades\Route;

// TaqnyatAdmin Plugin Routes
Route::get('/metrics', [TaqnyatAdminController::class, 'getMetrics']);
Route::get('/clients', [TaqnyatAdminController::class, 'getClients']);
Route::get('/subscriptions', [TaqnyatAdminController::class, 'getSubscriptions']);
Route::get('/leads', [TaqnyatAdminController::class, 'getLeads']);
Route::get('/invoices', [TaqnyatAdminController::class, 'getInvoices']);
Route::get('/revenue-analytics', [TaqnyatAdminController::class, 'getRevenueAnalytics']);
Route::get('/churn-analytics', [TaqnyatAdminController::class, 'getChurnAnalytics']);
