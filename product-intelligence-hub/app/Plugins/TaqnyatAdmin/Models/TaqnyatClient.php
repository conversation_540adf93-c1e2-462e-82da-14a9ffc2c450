<?php

namespace App\Plugins\TaqnyatAdmin\Models;

use Illuminate\Database\Eloquent\Model;

class TaqnyatClient extends Model
{
    protected $table = 'taqnyat_clients';
    
    protected $fillable = [
        'name',
        'email',
        'status',
        'subscription_plan',
        'monthly_revenue',
        'last_activity',
    ];

    protected $casts = [
        'monthly_revenue' => 'decimal:2',
        'last_activity' => 'date',
    ];

    /**
     * Scope for active clients
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for churned clients
     */
    public function scopeChurned($query)
    {
        return $query->where('status', 'churned');
    }

    /**
     * Get clients by subscription plan
     */
    public function scopeByPlan($query, $plan)
    {
        return $query->where('subscription_plan', $plan);
    }
}
