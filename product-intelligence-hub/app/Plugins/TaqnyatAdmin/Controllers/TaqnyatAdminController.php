<?php

namespace App\Plugins\TaqnyatAdmin\Controllers;

use App\Http\Controllers\Controller;
use App\Plugins\TaqnyatAdmin\Services\TaqnyatAdminService;
use Illuminate\Http\JsonResponse;

class TaqnyatAdminController extends Controller
{
    protected TaqnyatAdminService $service;

    public function __construct(TaqnyatAdminService $service)
    {
        $this->service = $service;
    }

    /**
     * Get dashboard metrics
     */
    public function getMetrics(): JsonResponse
    {
        $metrics = [
            'active_clients' => $this->service->getActiveClientsCount(),
            'total_monthly_revenue' => $this->service->getTotalMonthlyRevenue(),
            'churn_rate' => $this->service->calculateChurnRate(),
            'leads_conversion_rate' => $this->service->getLeadsConversionRate(),
            'revenue_by_product' => $this->service->getRevenueByProduct(),
        ];

        return response()->json([
            'success' => true,
            'data' => $metrics,
        ]);
    }

    /**
     * Get clients data
     */
    public function getClients(): JsonResponse
    {
        $clients = $this->service->getClients();

        return response()->json([
            'success' => true,
            'data' => $clients,
        ]);
    }

    /**
     * Get subscriptions data
     */
    public function getSubscriptions(): JsonResponse
    {
        $subscriptions = $this->service->getSubscriptions();

        return response()->json([
            'success' => true,
            'data' => $subscriptions,
        ]);
    }

    /**
     * Get leads data
     */
    public function getLeads(): JsonResponse
    {
        $leads = $this->service->getLeads();

        return response()->json([
            'success' => true,
            'data' => $leads,
        ]);
    }

    /**
     * Get invoices data
     */
    public function getInvoices(): JsonResponse
    {
        $invoices = $this->service->getInvoices();

        return response()->json([
            'success' => true,
            'data' => $invoices,
        ]);
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(): JsonResponse
    {
        $analytics = [
            'total_monthly_revenue' => $this->service->getTotalMonthlyRevenue(),
            'revenue_by_product' => $this->service->getRevenueByProduct(),
            'active_clients' => $this->service->getActiveClientsCount(),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get churn analytics
     */
    public function getChurnAnalytics(): JsonResponse
    {
        $analytics = [
            'churn_rate' => $this->service->calculateChurnRate(),
            'total_clients' => count($this->service->getClients()),
            'active_clients' => $this->service->getActiveClientsCount(),
            'churned_clients' => count(array_filter($this->service->getClients(), fn($client) => $client['status'] === 'churned')),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }
}
