<?php

namespace App\Plugins\TaqnyatAdmin\Services;

use Carbon\Carbon;

class TaqnyatAdminService
{
    /**
     * Get fake clients data
     */
    public function getClients(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Acme Corporation',
                'email' => '<EMAIL>',
                'status' => 'active',
                'subscription_plan' => 'enterprise',
                'monthly_revenue' => 5000,
                'created_at' => Carbon::now()->subMonths(12)->toDateString(),
                'last_activity' => Carbon::now()->subDays(2)->toDateString(),
            ],
            [
                'id' => 2,
                'name' => 'TechStart Solutions',
                'email' => '<EMAIL>',
                'status' => 'active',
                'subscription_plan' => 'professional',
                'monthly_revenue' => 2500,
                'created_at' => Carbon::now()->subMonths(8)->toDateString(),
                'last_activity' => Carbon::now()->subDays(1)->toDateString(),
            ],
            [
                'id' => 3,
                'name' => 'Digital Innovations Ltd',
                'email' => '<EMAIL>',
                'status' => 'churned',
                'subscription_plan' => 'basic',
                'monthly_revenue' => 0,
                'created_at' => Carbon::now()->subMonths(15)->toDateString(),
                'last_activity' => Carbon::now()->subMonths(2)->toDateString(),
            ],
            [
                'id' => 4,
                'name' => 'Global Enterprises',
                'email' => '<EMAIL>',
                'status' => 'active',
                'subscription_plan' => 'enterprise',
                'monthly_revenue' => 7500,
                'created_at' => Carbon::now()->subMonths(6)->toDateString(),
                'last_activity' => Carbon::now()->toDateString(),
            ],
            [
                'id' => 5,
                'name' => 'StartupHub',
                'email' => '<EMAIL>',
                'status' => 'trial',
                'subscription_plan' => 'professional',
                'monthly_revenue' => 0,
                'created_at' => Carbon::now()->subDays(15)->toDateString(),
                'last_activity' => Carbon::now()->toDateString(),
            ],
        ];
    }

    /**
     * Get fake subscriptions data
     */
    public function getSubscriptions(): array
    {
        return [
            [
                'id' => 1,
                'client_id' => 1,
                'plan' => 'enterprise',
                'status' => 'active',
                'monthly_amount' => 5000,
                'start_date' => Carbon::now()->subMonths(12)->toDateString(),
                'next_billing' => Carbon::now()->addDays(15)->toDateString(),
            ],
            [
                'id' => 2,
                'client_id' => 2,
                'plan' => 'professional',
                'status' => 'active',
                'monthly_amount' => 2500,
                'start_date' => Carbon::now()->subMonths(8)->toDateString(),
                'next_billing' => Carbon::now()->addDays(22)->toDateString(),
            ],
            [
                'id' => 3,
                'client_id' => 4,
                'plan' => 'enterprise',
                'status' => 'active',
                'monthly_amount' => 7500,
                'start_date' => Carbon::now()->subMonths(6)->toDateString(),
                'next_billing' => Carbon::now()->addDays(8)->toDateString(),
            ],
            [
                'id' => 4,
                'client_id' => 5,
                'plan' => 'professional',
                'status' => 'trial',
                'monthly_amount' => 0,
                'start_date' => Carbon::now()->subDays(15)->toDateString(),
                'next_billing' => Carbon::now()->addDays(15)->toDateString(),
            ],
        ];
    }

    /**
     * Get fake leads data
     */
    public function getLeads(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Future Tech Inc',
                'email' => '<EMAIL>',
                'status' => 'qualified',
                'source' => 'website',
                'estimated_value' => 3000,
                'created_at' => Carbon::now()->subDays(5)->toDateString(),
            ],
            [
                'id' => 2,
                'name' => 'Innovation Labs',
                'email' => '<EMAIL>',
                'status' => 'contacted',
                'source' => 'referral',
                'estimated_value' => 5000,
                'created_at' => Carbon::now()->subDays(10)->toDateString(),
            ],
            [
                'id' => 3,
                'name' => 'Smart Solutions',
                'email' => '<EMAIL>',
                'status' => 'new',
                'source' => 'social_media',
                'estimated_value' => 2000,
                'created_at' => Carbon::now()->subDays(2)->toDateString(),
            ],
        ];
    }

    /**
     * Get fake invoices data
     */
    public function getInvoices(): array
    {
        return [
            [
                'id' => 1,
                'client_id' => 1,
                'amount' => 5000,
                'status' => 'paid',
                'due_date' => Carbon::now()->subDays(30)->toDateString(),
                'paid_date' => Carbon::now()->subDays(25)->toDateString(),
                'created_at' => Carbon::now()->subDays(35)->toDateString(),
            ],
            [
                'id' => 2,
                'client_id' => 2,
                'amount' => 2500,
                'status' => 'paid',
                'due_date' => Carbon::now()->subDays(15)->toDateString(),
                'paid_date' => Carbon::now()->subDays(10)->toDateString(),
                'created_at' => Carbon::now()->subDays(20)->toDateString(),
            ],
            [
                'id' => 3,
                'client_id' => 4,
                'amount' => 7500,
                'status' => 'pending',
                'due_date' => Carbon::now()->addDays(5)->toDateString(),
                'paid_date' => null,
                'created_at' => Carbon::now()->subDays(5)->toDateString(),
            ],
        ];
    }

    /**
     * Calculate churn rate
     */
    public function calculateChurnRate(): float
    {
        $clients = $this->getClients();
        $totalClients = count($clients);
        $churnedClients = count(array_filter($clients, fn($client) => $client['status'] === 'churned'));
        
        return $totalClients > 0 ? round(($churnedClients / $totalClients) * 100, 2) : 0;
    }

    /**
     * Calculate revenue by product/plan
     */
    public function getRevenueByProduct(): array
    {
        $subscriptions = $this->getSubscriptions();
        $revenue = [];
        
        foreach ($subscriptions as $subscription) {
            if ($subscription['status'] === 'active') {
                $plan = $subscription['plan'];
                if (!isset($revenue[$plan])) {
                    $revenue[$plan] = 0;
                }
                $revenue[$plan] += $subscription['monthly_amount'];
            }
        }
        
        return $revenue;
    }

    /**
     * Get active clients count
     */
    public function getActiveClientsCount(): int
    {
        $clients = $this->getClients();
        return count(array_filter($clients, fn($client) => $client['status'] === 'active'));
    }

    /**
     * Get total monthly revenue
     */
    public function getTotalMonthlyRevenue(): float
    {
        $clients = $this->getClients();
        return array_sum(array_column($clients, 'monthly_revenue'));
    }

    /**
     * Get leads conversion rate
     */
    public function getLeadsConversionRate(): float
    {
        $leads = $this->getLeads();
        $totalLeads = count($leads);
        $qualifiedLeads = count(array_filter($leads, fn($lead) => $lead['status'] === 'qualified'));
        
        return $totalLeads > 0 ? round(($qualifiedLeads / $totalLeads) * 100, 2) : 0;
    }
}
