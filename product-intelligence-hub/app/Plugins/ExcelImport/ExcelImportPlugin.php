<?php

namespace App\Plugins\ExcelImport;

use App\Plugins\BasePlugin;

class ExcelImportPlugin extends BasePlugin
{
    protected string $name = 'ExcelImport';
    protected string $description = 'Manual Excel sheet import and data analysis';
    protected string $version = '1.0.0';
    protected bool $enabled = false; // Disabled by default as placeholder

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'import_status',
                'title' => 'Import Status',
                'component' => 'ExcelImportStatusWidget',
                'size' => 'medium',
                'order' => 50,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'data_summary',
                'title' => 'Imported Data Summary',
                'component' => 'ExcelDataSummaryWidget',
                'size' => 'large',
                'order' => 51,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'upload_path' => [
                'type' => 'string',
                'label' => 'Upload Path',
                'description' => 'Directory path for Excel file uploads',
                'required' => false,
                'default' => 'storage/excel-imports',
            ],
            'max_file_size' => [
                'type' => 'integer',
                'label' => 'Max File Size (MB)',
                'description' => 'Maximum allowed file size for uploads',
                'required' => false,
                'default' => 10,
            ],
            'allowed_extensions' => [
                'type' => 'string',
                'label' => 'Allowed Extensions',
                'description' => 'Comma-separated list of allowed file extensions',
                'required' => false,
                'default' => 'xlsx,xls,csv',
            ],
            'auto_process' => [
                'type' => 'boolean',
                'label' => 'Auto Process',
                'description' => 'Automatically process uploaded files',
                'required' => false,
                'default' => true,
            ],
            'notification_email' => [
                'type' => 'string',
                'label' => 'Notification Email',
                'description' => 'Email address for import notifications',
                'required' => false,
            ],
        ];
    }
}
