<?php

namespace App\Plugins\ClickUp;

use App\Plugins\BasePlugin;

class ClickUpPlugin extends BasePlugin
{
    protected string $name = 'ClickUp';
    protected string $description = 'Task progress tracking and project management integration';
    protected string $version = '1.0.0';
    protected bool $enabled = false; // Disabled by default as placeholder

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'task_progress',
                'title' => 'Task Progress',
                'component' => 'ClickUpTaskProgressWidget',
                'size' => 'medium',
                'order' => 10,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'overdue_tasks',
                'title' => 'Overdue Tasks',
                'component' => 'ClickUpOverdueTasksWidget',
                'size' => 'small',
                'order' => 11,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'api_token' => [
                'type' => 'string',
                'label' => 'API Token',
                'description' => 'Your ClickUp API token',
                'required' => true,
                'sensitive' => true,
            ],
            'team_id' => [
                'type' => 'string',
                'label' => 'Team ID',
                'description' => 'ClickUp team ID to track',
                'required' => true,
            ],
            'sync_interval' => [
                'type' => 'integer',
                'label' => 'Sync Interval (minutes)',
                'description' => 'How often to sync task data',
                'required' => false,
                'default' => 30,
            ],
        ];
    }
}
