<?php

namespace App\Plugins\Outlook;

use App\Plugins\BasePlugin;

class OutlookPlugin extends BasePlugin
{
    protected string $name = 'Outlook';
    protected string $description = 'Client feedback analysis via email integration';
    protected string $version = '1.0.0';
    protected bool $enabled = false; // Disabled by default as placeholder

    public function registerWidgets(): array
    {
        return [
            [
                'plugin' => $this->getName(),
                'name' => 'client_feedback',
                'title' => 'Client Feedback',
                'component' => 'OutlookFeedbackWidget',
                'size' => 'large',
                'order' => 20,
            ],
            [
                'plugin' => $this->getName(),
                'name' => 'email_sentiment',
                'title' => 'Email Sentiment',
                'component' => 'OutlookSentimentWidget',
                'size' => 'medium',
                'order' => 21,
            ],
        ];
    }

    public function getConfigSchema(): array
    {
        return [
            'client_id' => [
                'type' => 'string',
                'label' => 'Client ID',
                'description' => 'Microsoft Graph API Client ID',
                'required' => true,
                'sensitive' => true,
            ],
            'client_secret' => [
                'type' => 'string',
                'label' => 'Client Secret',
                'description' => 'Microsoft Graph API Client Secret',
                'required' => true,
                'sensitive' => true,
            ],
            'tenant_id' => [
                'type' => 'string',
                'label' => 'Tenant ID',
                'description' => 'Microsoft Azure Tenant ID',
                'required' => true,
            ],
            'mailbox' => [
                'type' => 'string',
                'label' => 'Mailbox',
                'description' => 'Email address to monitor for feedback',
                'required' => true,
            ],
            'keywords' => [
                'type' => 'string',
                'label' => 'Feedback Keywords',
                'description' => 'Comma-separated keywords to identify feedback emails',
                'required' => false,
                'default' => 'feedback,complaint,suggestion,issue',
            ],
        ];
    }
}
