<?php

namespace App\Plugins;

use App\Contracts\PluginInterface;
use Illuminate\Support\Facades\Route;

abstract class BasePlugin implements PluginInterface
{
    protected string $name;
    protected string $description;
    protected string $version = '1.0.0';
    protected bool $enabled = true;

    public function getName(): string
    {
        return $this->name ?? class_basename(static::class);
    }

    public function getDescription(): string
    {
        return $this->description ?? 'No description provided';
    }

    public function getVersion(): string
    {
        return $this->version;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function registerRoutes(): void
    {
        $routesPath = $this->getPluginPath() . '/routes.php';
        if (file_exists($routesPath)) {
            Route::prefix('api/plugins/' . strtolower($this->getName()))
                ->middleware(['api'])
                ->group($routesPath);
        }
    }

    public function registerWidgets(): array
    {
        return [];
    }

    public function getConfigSchema(): array
    {
        return [];
    }

    public function boot(): void
    {
        // Override in child classes if needed
    }

    /**
     * Get the plugin directory path
     */
    protected function getPluginPath(): string
    {
        $reflection = new \ReflectionClass(static::class);
        return dirname($reflection->getFileName());
    }

    /**
     * Get plugin configuration
     */
    protected function getConfig(string $key = null, $default = null)
    {
        $pluginName = strtolower($this->getName());
        $config = config("plugins.{$pluginName}", []);
        
        if ($key === null) {
            return $config;
        }
        
        return data_get($config, $key, $default);
    }
}
