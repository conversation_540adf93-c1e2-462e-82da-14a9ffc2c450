@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-cog text-blue-600 mr-3"></i>
            Settings
        </h1>
        <p class="mt-2 text-gray-600">Configure your plugins and system preferences</p>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Error Messages -->
    @if($errors->any())
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission:</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Plugin Settings -->
    <div class="space-y-6">
        @forelse($pluginConfigs as $pluginName => $pluginData)
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $pluginData['plugin']->getName() }}</h3>
                            <p class="text-sm text-gray-600">{{ $pluginData['plugin']->getDescription() }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                v{{ $pluginData['plugin']->getVersion() }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $pluginData['plugin']->isEnabled() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $pluginData['plugin']->isEnabled() ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>
                </div>

                @if(!empty($pluginData['schema']))
                    <form method="POST" action="{{ route('settings.update') }}" class="p-6">
                        @csrf
                        <input type="hidden" name="plugin" value="{{ $pluginData['plugin']->getName() }}">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($pluginData['schema'] as $fieldName => $field)
                                <div class="space-y-2">
                                    <label for="{{ $pluginName }}_{{ $fieldName }}" class="block text-sm font-medium text-gray-700">
                                        {{ $field['label'] }}
                                        @if($field['required'])
                                            <span class="text-red-500">*</span>
                                        @endif
                                    </label>
                                    
                                    @if($field['type'] === 'string')
                                        <input type="{{ isset($field['sensitive']) && $field['sensitive'] ? 'password' : 'text' }}"
                                               id="{{ $pluginName }}_{{ $fieldName }}"
                                               name="config[{{ $fieldName }}]"
                                               value="{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '') }}"
                                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                               @if($field['required']) required @endif>
                                    @elseif($field['type'] === 'integer')
                                        <input type="number"
                                               id="{{ $pluginName }}_{{ $fieldName }}"
                                               name="config[{{ $fieldName }}]"
                                               value="{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '') }}"
                                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                               @if($field['required']) required @endif>
                                    @elseif($field['type'] === 'boolean')
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   id="{{ $pluginName }}_{{ $fieldName }}"
                                                   name="config[{{ $fieldName }}]"
                                                   value="1"
                                                   {{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? false) ? 'checked' : '' }}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="{{ $pluginName }}_{{ $fieldName }}" class="ml-2 block text-sm text-gray-900">
                                                Enable this option
                                            </label>
                                        </div>
                                    @endif
                                    
                                    @if(isset($field['description']))
                                        <p class="text-xs text-gray-500">{{ $field['description'] }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                                <i class="fas fa-save mr-2"></i>
                                Save Settings
                            </button>
                        </div>
                    </form>
                @else
                    <div class="p-6">
                        <p class="text-gray-500 text-center">This plugin has no configurable settings.</p>
                    </div>
                @endif
            </div>
        @empty
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-8 text-center">
                <i class="fas fa-plug text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Plugins Found</h3>
                <p class="text-gray-600">No plugins are currently installed or enabled.</p>
            </div>
        @endforelse
    </div>

    <!-- System Information -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
            System Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">Laravel Version:</span>
                <span class="text-gray-600">{{ app()->version() }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">PHP Version:</span>
                <span class="text-gray-600">{{ PHP_VERSION }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Environment:</span>
                <span class="text-gray-600">{{ app()->environment() }}</span>
            </div>
        </div>
    </div>
</div>
@endsection
