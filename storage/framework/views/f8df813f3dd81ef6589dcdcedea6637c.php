<?php $__env->startSection('page-title', $page->name); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="<?php echo e($page->icon ?: 'fas fa-chart-bar'); ?> text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo e($page->name); ?></h1>
                    <?php if($page->description): ?>
                        <p class="text-gray-600"><?php echo e($page->description); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <?php if(!$page->is_published): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-eye-slash mr-1"></i>
                        Draft
                    </span>
                <?php endif; ?>
                <a href="/clickup/builder/<?php echo e($page->id); ?>" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-paint-brush mr-2"></i>
                    Edit Page
                </a>
            </div>
        </div>
    </div>

    <!-- Widget Grid -->
    <div class="grid grid-cols-12 gap-6" style="grid-auto-rows: minmax(200px, auto);">
        <?php $__currentLoopData = $widgets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $widget): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($widget['type'] !== 'error'): ?>
                <div class="col-span-<?php echo e($widget['config']['position']['width'] ?? 6); ?> 
                           row-span-<?php echo e($widget['config']['position']['height'] ?? 2); ?>"
                     style="grid-column-start: <?php echo e(($widget['config']['position']['x'] ?? 0) + 1); ?>; 
                            grid-row-start: <?php echo e(($widget['config']['position']['y'] ?? 0) + 1); ?>;">
                    
                    <?php if($widget['type'] === 'table'): ?>
                        <?php echo $__env->make('clickup::widgets.table', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php elseif($widget['type'] === 'number_card'): ?>
                        <?php echo $__env->make('clickup::widgets.number-card', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php elseif($widget['type'] === 'progress_card'): ?>
                        <?php echo $__env->make('clickup::widgets.progress-card', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php elseif($widget['type'] === 'status_chart'): ?>
                        <?php echo $__env->make('clickup::widgets.status-chart', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php elseif($widget['type'] === 'timeline_card'): ?>
                        <?php echo $__env->make('clickup::widgets.timeline-card', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php elseif($widget['type'] === 'assignee_chart'): ?>
                        <?php echo $__env->make('clickup::widgets.assignee-chart', ['widget' => $widget], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <!-- Error Widget -->
                <div class="col-span-6 row-span-1">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800"><?php echo e($widget['title']); ?></h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <?php echo e($widget['data']['message']); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        <?php if(empty($widgets)): ?>
            <!-- Empty State -->
            <div class="col-span-12 flex items-center justify-center h-64 border-2 border-dashed border-gray-300 rounded-lg">
                <div class="text-center">
                    <i class="fas fa-paint-brush text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Widgets Added</h3>
                    <p class="text-gray-500 mb-4">Use the page builder to add widgets and create your custom dashboard</p>
                    <a href="/clickup/builder/<?php echo e($page->id); ?>" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-paint-brush mr-2"></i>
                        Open Page Builder
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Real-time Updates -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh widgets every 5 minutes
    setInterval(function() {
        // Refresh page data
        window.location.reload();
    }, 300000); // 5 minutes
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('clickup::layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/widget-page.blade.php ENDPATH**/ ?>