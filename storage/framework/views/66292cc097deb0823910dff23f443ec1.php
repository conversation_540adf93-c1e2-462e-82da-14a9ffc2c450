<?php $__env->startSection('page-title', $pageTitle ?? 'ClickUp'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-tasks text-blue-600 mr-3"></i>
                    <?php echo e($pageTitle ?? 'ClickUp'); ?>

                </h1>
                <p class="mt-2 text-gray-600"><?php echo e($pageDescription ?? 'ClickUp integration dashboard'); ?></p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('plugins.settings', 'ClickUp')); ?>"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-cog mr-2"></i>
                    Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Error Message -->
    <?php if(isset($error)): ?>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Error Loading Data</h3>
                    <p class="mt-1 text-sm text-red-700"><?php echo e($error); ?></p>
                    <div class="mt-3">
                        <a href="<?php echo e(route('plugins.settings', 'ClickUp')); ?>" 
                           class="text-sm text-red-600 hover:text-red-500 underline">
                            Check ClickUp Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Navigation Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="<?php echo e(route('clickup.tasks')); ?>" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('clickup.tasks') ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'); ?>">
                <i class="fas fa-tasks mr-2"></i>
                Tasks
            </a>
            <a href="<?php echo e(route('clickup.progress')); ?>" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('clickup.progress') ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'); ?>">
                <i class="fas fa-chart-line mr-2"></i>
                Progress
            </a>
            <a href="<?php echo e(route('clickup.team')); ?>" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('clickup.team') ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'); ?>">
                <i class="fas fa-users mr-2"></i>
                Team
            </a>
            <a href="<?php echo e(route('clickup.overdue')); ?>" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('clickup.overdue') ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'); ?>">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Overdue
            </a>
        </nav>
    </div>

    <!-- Page Content -->
    <?php echo $__env->yieldContent('page-content'); ?>
</div>

<!-- Chart.js for data visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Custom ClickUp JavaScript -->
<script>
// Common ClickUp functionality
window.ClickUpDashboard = {
    // Initialize charts and interactive elements
    init() {
        this.initializeCharts();
        this.initializeFilters();
    },
    
    // Initialize chart components
    initializeCharts() {
        // This will be extended by individual pages
    },
    
    // Initialize filter functionality
    initializeFilters() {
        // This will be extended by individual pages
    },
    
    // Utility function to format dates
    formatDate(dateString) {
        if (!dateString) return 'No date';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    },
    
    // Utility function to get status color
    getStatusColor(status) {
        const colors = {
            'open': '#6B7280',
            'in_progress': '#3B82F6', 
            'completed': '#10B981',
            'overdue': '#EF4444',
            'blocked': '#F59E0B'
        };
        return colors[status] || '#6B7280';
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.ClickUpDashboard.init();
});
</script>

<?php echo $__env->yieldContent('page-scripts'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/layout.blade.php ENDPATH**/ ?>