<?php $__env->startSection('page-title', $pluginName . ' Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-cog text-blue-600 mr-3"></i>
                    <?php echo e($pluginName); ?> Settings
                </h1>
                <p class="mt-2 text-gray-600">Configure your <?php echo e($pluginName); ?> plugin settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('plugins.manage')); ?>" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plugin Manager
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <p class="text-sm font-medium text-green-800"><?php echo e(session('success')); ?></p>
            </div>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div class="text-sm text-red-700">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <p><?php echo e($error); ?></p>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Plugin Settings -->
    <?php if($settingsView): ?>
        <?php echo $settingsView; ?>

    <?php else: ?>
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-8 text-center">
            <i class="fas fa-cog text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Settings Available</h3>
            <p class="text-gray-600">This plugin does not have a custom settings interface.</p>
            
            <!-- Generic Settings Form -->
            <div class="mt-6 max-w-md mx-auto">
                <form method="POST" action="<?php echo e(route('plugins.settings.update', $pluginName)); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="space-y-4">
                        <div>
                            <label class="flex items-center justify-center">
                                <input type="checkbox"
                                       name="plugin_enabled"
                                       value="1"
                                       <?php echo e(($config['plugin_enabled'] ?? true) ? 'checked' : ''); ?>

                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Enable Plugin</span>
                            </label>
                        </div>
                        <button type="submit" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                            <i class="fas fa-save mr-2"></i>
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Plugin Information -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
            Plugin Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">Plugin Name:</span>
                <span class="text-gray-600"><?php echo e($plugin->getName()); ?></span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Version:</span>
                <span class="text-gray-600"><?php echo e($plugin->getVersion()); ?></span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Status:</span>
                <span class="text-gray-600"><?php echo e($plugin->isEnabled() ? 'Enabled' : 'Disabled'); ?></span>
            </div>
        </div>
        <div class="mt-4">
            <span class="font-medium text-gray-700">Description:</span>
            <span class="text-gray-600"><?php echo e($plugin->getDescription()); ?></span>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/resources/views/plugins/settings.blade.php ENDPATH**/ ?>