<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-cog text-blue-600 mr-3"></i>
            Settings
        </h1>
        <p class="mt-2 text-gray-600">Configure your plugins and system preferences</p>
    </div>

    <!-- Success Message -->
    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800"><?php echo e(session('success')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if($errors->any()): ?>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission:</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Plugin Settings -->
    <div class="space-y-6">
        <?php $__empty_1 = true; $__currentLoopData = $pluginConfigs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pluginName => $pluginData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo e($pluginData['plugin']->getName()); ?></h3>
                            <p class="text-sm text-gray-600"><?php echo e($pluginData['plugin']->getDescription()); ?></p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                v<?php echo e($pluginData['plugin']->getVersion()); ?>

                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($pluginData['plugin']->isEnabled() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e($pluginData['plugin']->isEnabled() ? 'Enabled' : 'Disabled'); ?>

                            </span>
                        </div>
                    </div>
                </div>

                <?php if(!empty($pluginData['schema'])): ?>
                    <form method="POST" action="<?php echo e(route('settings.update')); ?>" class="p-6"
                          x-data="pluginSettings('<?php echo e($pluginData['plugin']->getName()); ?>')"
                          x-init="init()">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="plugin" value="<?php echo e($pluginData['plugin']->getName()); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php $__currentLoopData = $pluginData['schema']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldName => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="space-y-2">
                                    <label for="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>" class="block text-sm font-medium text-gray-700">
                                        <?php echo e($field['label']); ?>

                                        <?php if($field['required']): ?>
                                            <span class="text-red-500">*</span>
                                        <?php endif; ?>
                                    </label>
                                    
                                    <?php if($field['type'] === 'string'): ?>
                                        <?php if($fieldName === 'space_id' && $pluginName === 'clickup'): ?>
                                            <!-- Dynamic Space ID Dropdown for ClickUp -->
                                            <div class="space-y-2">
                                                <div x-show="loadingSpaces" class="flex items-center space-x-2">
                                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                                    <span class="text-sm text-gray-600">Loading spaces...</span>
                                                </div>

                                                <div x-show="!loadingSpaces && !spacesError && spaces.length > 0">
                                                    <select id="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>"
                                                            name="config[<?php echo e($fieldName); ?>]"
                                                            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                            <?php if($field['required']): ?> required <?php endif; ?>>
                                                        <option value="">Select a space...</option>
                                                        <template x-for="space in spaces" :key="space.id">
                                                            <option :value="space.id"
                                                                    :selected="space.id === '<?php echo e(old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? '')); ?>'"
                                                                    x-text="`${space.name} (${space.team_name})`"></option>
                                                        </template>
                                                    </select>
                                                </div>

                                                <div x-show="!loadingSpaces && (spacesError || spaces.length === 0)">
                                                    <div x-show="spacesError" class="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        <span x-text="spacesError"></span>
                                                    </div>
                                                    <input type="text"
                                                           id="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>_fallback"
                                                           name="config[<?php echo e($fieldName); ?>]"
                                                           value="<?php echo e(old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '')); ?>"
                                                           placeholder="Enter Space ID manually"
                                                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                           <?php if($field['required']): ?> required <?php endif; ?>>
                                                    <p class="mt-1 text-xs text-gray-500">
                                                        Unable to load spaces automatically. Please enter your Space ID manually.
                                                    </p>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <input type="<?php echo e(isset($field['sensitive']) && $field['sensitive'] ? 'password' : 'text'); ?>"
                                                   id="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>"
                                                   name="config[<?php echo e($fieldName); ?>]"
                                                   value="<?php echo e(old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '')); ?>"
                                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                   <?php if($field['required']): ?> required <?php endif; ?>>
                                        <?php endif; ?>
                                    <?php elseif($field['type'] === 'integer'): ?>
                                        <input type="number"
                                               id="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>"
                                               name="config[<?php echo e($fieldName); ?>]"
                                               value="<?php echo e(old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '')); ?>"
                                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                               <?php if($field['required']): ?> required <?php endif; ?>>
                                    <?php elseif($field['type'] === 'boolean'): ?>
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   id="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>"
                                                   name="config[<?php echo e($fieldName); ?>]"
                                                   value="1"
                                                   <?php echo e(old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? false) ? 'checked' : ''); ?>

                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="<?php echo e($pluginName); ?>_<?php echo e($fieldName); ?>" class="ml-2 block text-sm text-gray-900">
                                                Enable this option
                                            </label>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if(isset($field['description'])): ?>
                                        <p class="text-xs text-gray-500"><?php echo e($field['description']); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                                <i class="fas fa-save mr-2"></i>
                                Save Settings
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="p-6">
                        <p class="text-gray-500 text-center">This plugin has no configurable settings.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-8 text-center">
                <i class="fas fa-plug text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Plugins Found</h3>
                <p class="text-gray-600">No plugins are currently installed or enabled.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- System Information -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
            System Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">Laravel Version:</span>
                <span class="text-gray-600"><?php echo e(app()->version()); ?></span>
            </div>
            <div>
                <span class="font-medium text-gray-700">PHP Version:</span>
                <span class="text-gray-600"><?php echo e(PHP_VERSION); ?></span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Environment:</span>
                <span class="text-gray-600"><?php echo e(app()->environment()); ?></span>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function pluginSettings(pluginName) {
    return {
        pluginName: pluginName,
        loadingSpaces: false,
        spaces: [],
        spacesError: null,

        init() {
            if (this.pluginName.toLowerCase() === 'clickup') {
                this.loadSpaces();
            }
        },

        async loadSpaces() {
            this.loadingSpaces = true;
            this.spacesError = null;

            try {
                const response = await fetch('/api/plugins/clickup/spaces');
                const data = await response.json();

                if (data.success) {
                    this.spaces = data.spaces || [];
                    if (this.spaces.length === 0) {
                        this.spacesError = 'No spaces found. Please check your API token and permissions.';
                    }
                } else {
                    this.spacesError = data.message || 'Failed to load spaces from ClickUp API.';
                }
            } catch (error) {
                console.error('Error loading ClickUp spaces:', error);
                this.spacesError = 'Network error: Unable to connect to ClickUp API.';
            } finally {
                this.loadingSpaces = false;
            }
        }
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/resources/views/settings/index.blade.php ENDPATH**/ ?>