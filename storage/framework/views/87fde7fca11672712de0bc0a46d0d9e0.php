<?php $__env->startSection('page-content'); ?>
<!-- Progress Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <?php
        $totalTasks = ($progress['total_tasks'] ?? 0);
        $completedTasks = ($progress['completed_tasks'] ?? 0);
        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0;
    ?>
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line text-blue-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($completionRate); ?>%</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-tasks text-green-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Completed Tasks</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($completedTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Remaining Tasks</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($totalTasks - $completedTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Completion Progress Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Overall Progress</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="progressChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Status Distribution Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Task Status Distribution</h3>
        </div>
        <div class="p-6">
            <div class="relative">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Progress by List -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Progress by List</h3>
    </div>
    <div class="overflow-hidden">
        <?php if(count($lists) > 0): ?>
            <ul class="divide-y divide-gray-200">
                <?php $__currentLoopData = $lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $listTasks = collect($tasksByStatus)->flatten()->where('list.id', $list['id']);
                        $listTotal = $listTasks->count();
                        $listCompleted = $listTasks->where('status.status', 'completed')->count();
                        $listProgress = $listTotal > 0 ? round(($listCompleted / $listTotal) * 100, 1) : 0;
                    ?>
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="min-w-0 flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">
                                        <?php echo e($list['name']); ?>

                                    </h4>
                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                        <span><?php echo e($listCompleted); ?>/<?php echo e($listTotal); ?> tasks completed</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ml-4">
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($listProgress); ?>%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900"><?php echo e($listProgress); ?>%</span>
                                </div>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        <?php else: ?>
            <div class="px-6 py-12 text-center">
                <i class="fas fa-list text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No lists found</h3>
                <p class="text-gray-500">
                    <?php if(isset($error)): ?>
                        There was an error loading lists. Please check your ClickUp configuration.
                    <?php else: ?>
                        No lists are available in your configured space.
                    <?php endif; ?>
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Recent Activity -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Progress Insights</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-3">Completion Trends</h4>
                <div class="space-y-3">
                    <?php if($completionRate >= 80): ?>
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-arrow-up mr-2"></i>
                            <span class="text-sm">Excellent progress! <?php echo e($completionRate); ?>% completion rate</span>
                        </div>
                    <?php elseif($completionRate >= 60): ?>
                        <div class="flex items-center text-yellow-600">
                            <i class="fas fa-arrow-right mr-2"></i>
                            <span class="text-sm">Good progress. <?php echo e($completionRate); ?>% completion rate</span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center text-red-600">
                            <i class="fas fa-arrow-down mr-2"></i>
                            <span class="text-sm">Needs attention. <?php echo e($completionRate); ?>% completion rate</span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($totalTasks > 0): ?>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-info-circle mr-2"></i>
                            <span class="text-sm"><?php echo e($totalTasks - $completedTasks); ?> tasks remaining</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-3">Recommendations</h4>
                <div class="space-y-3">
                    <?php if($completionRate < 50): ?>
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                            <span class="text-sm">Focus on completing high-priority tasks first</span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if(count($lists) > 3): ?>
                        <div class="flex items-start text-gray-600">
                            <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                            <span class="text-sm">Consider consolidating similar lists for better focus</span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="flex items-start text-gray-600">
                        <i class="fas fa-lightbulb mr-2 mt-0.5"></i>
                        <span class="text-sm">Review overdue tasks to improve completion rates</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-scripts'); ?>
<script>
// Progress page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Progress Chart
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    new Chart(progressCtx, {
        type: 'doughnut',
        data: {
            labels: ['Completed', 'Remaining'],
            datasets: [{
                data: [<?php echo e($completedTasks); ?>, <?php echo e($totalTasks - $completedTasks); ?>],
                backgroundColor: ['#10B981', '#E5E7EB'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = <?php echo json_encode($tasksByStatus, 15, 512) ?>;
    
    new Chart(statusCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(statusData),
            datasets: [{
                label: 'Tasks',
                data: Object.values(statusData).map(tasks => tasks.length),
                backgroundColor: [
                    '#6B7280', // open
                    '#3B82F6', // in_progress  
                    '#10B981', // completed
                    '#EF4444', // overdue
                    '#F59E0B'  // blocked
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('clickup::layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/progress.blade.php ENDPATH**/ ?>