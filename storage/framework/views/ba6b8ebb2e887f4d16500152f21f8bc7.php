<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Product Intelligence Hub')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Figtree', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-50" x-data="{ sidebarOpen: false }">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
             :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">

            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                <h1 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                    Intelligence Hub
                </h1>
                <button @click="sidebarOpen = false" class="lg:hidden">
                    <i class="fas fa-times text-gray-500"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="mt-4 px-4">
                <div class="space-y-2">
                    <!-- Core Navigation -->
                    <a href="<?php echo e(route('dashboard')); ?>"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('dashboard') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Plugin Navigation Items -->
                    <?php
                        $pluginManager = app(\App\Services\PluginManager::class);
                        $sidebarItems = [];
                        foreach ($pluginManager->getPlugins() as $plugin) {
                            $items = $plugin->registerSidebarItems();
                            $sidebarItems = array_merge($sidebarItems, $items);
                        }
                    ?>

                    <?php if(!empty($sidebarItems)): ?>
                        <div class="pt-4">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Plugins</h3>
                            <div class="mt-2 space-y-1">
                                <?php $__currentLoopData = $sidebarItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(isset($item['children']) && !empty($item['children'])): ?>
                                        <!-- Dropdown Menu -->
                                        <div x-data="{ open: false }">
                                            <button @click="open = !open"
                                                    class="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100">
                                                <div class="flex items-center">
                                                    <i class="<?php echo e($item['icon'] ?? 'fas fa-puzzle-piece'); ?> mr-3"></i>
                                                    <?php echo e($item['title']); ?>

                                                </div>
                                                <i class="fas fa-chevron-down transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                                            </button>
                                            <div x-show="open" x-transition class="ml-6 mt-1 space-y-1">
                                                <?php $__currentLoopData = $item['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e($child['url']); ?>"
                                                       class="flex items-center px-3 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-100">
                                                        <i class="<?php echo e($child['icon'] ?? 'fas fa-circle'); ?> mr-3 text-xs"></i>
                                                        <?php echo e($child['title']); ?>

                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- Single Menu Item -->
                                        <a href="<?php echo e($item['url']); ?>"
                                           class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100">
                                            <i class="<?php echo e($item['icon'] ?? 'fas fa-puzzle-piece'); ?> mr-3"></i>
                                            <?php echo e($item['title']); ?>

                                        </a>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Settings -->
                    <div class="pt-4 border-t border-gray-200">
                        <a href="<?php echo e(route('settings')); ?>"
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('settings') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                            <i class="fas fa-cog mr-3"></i>
                            Settings
                        </a>
                        <a href="<?php echo e(route('plugins.manage') ?? '#'); ?>"
                           class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100">
                            <i class="fas fa-plug mr-3"></i>
                            Plugin Manager
                        </a>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Sidebar Overlay for Mobile -->
        <div x-show="sidebarOpen"
             @click="sidebarOpen = false"
             class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"></div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Top Navigation -->
            <nav class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button @click="sidebarOpen = true" class="lg:hidden mr-4">
                                <i class="fas fa-bars text-gray-500"></i>
                            </button>
                            <h2 class="text-xl font-semibold text-gray-900">
                                <?php echo $__env->yieldContent('page-title', 'Dashboard'); ?>
                            </h2>
                        </div>
                        <div class="flex items-center">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-user-circle mr-1"></i>
                                Head of Product
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Heading -->
            <?php if(isset($header)): ?>
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo e($header); ?>

                    </div>
                </header>
            <?php endif; ?>

            <!-- Page Content -->
            <main class="py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /Users/<USER>/Herd/team/resources/views/layouts/app.blade.php ENDPATH**/ ?>