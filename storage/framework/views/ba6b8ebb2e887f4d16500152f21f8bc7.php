<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Product Intelligence Hub')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Figtree', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Plugin Menu Styles -->
    <style>
        .plugin-menu {
            margin-bottom: 0.5rem;
        }
        .plugin-menu:last-child {
            margin-bottom: 0;
        }
        .plugin-menu button:hover .fa-chevron-down {
            color: #3b82f6;
        }
        .plugin-menu [x-show="open"] {
            background: linear-gradient(to right, transparent, rgba(59, 130, 246, 0.05));
            border-radius: 0.5rem;
            margin-top: 0.25rem;
            padding: 0.25rem 0;
        }
        .plugin-menu .border-l-2 {
            position: relative;
        }
        .plugin-menu .border-l-2::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6, #60a5fa);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50" x-data="{ sidebarOpen: false }">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
             :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">

            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                <h1 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                    Intelligence Hub
                </h1>
                <button @click="sidebarOpen = false" class="lg:hidden">
                    <i class="fas fa-times text-gray-500"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="mt-4 px-4">
                <div class="space-y-2">
                    <!-- Core Navigation -->
                    <a href="<?php echo e(route('dashboard')); ?>"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('dashboard') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Plugin Navigation Items -->
                    <?php
                        $pluginManager = app(\App\Services\PluginManager::class);
                        $pluginMenus = [];

                        // Plugin icon mapping
                        $pluginIcons = [
                            'ClickUp' => 'fas fa-tasks',
                            'TaqnyatAdmin' => 'fas fa-building',
                            'TestPlugin' => 'fas fa-flask',
                        ];

                        foreach ($pluginManager->getPlugins() as $plugin) {
                            $items = $plugin->registerSidebarItems();
                            if (!empty($items)) {
                                $pluginName = $plugin->getName();
                                $pluginMenus[] = [
                                    'title' => $pluginName,
                                    'icon' => $pluginIcons[$pluginName] ?? 'fas fa-puzzle-piece',
                                    'children' => $items,
                                    'enabled' => $plugin->isEnabled()
                                ];
                            }
                        }
                    ?>

                    <?php if(!empty($pluginMenus)): ?>
                        <div class="pt-4">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Plugins</h3>
                            <div class="mt-2 space-y-1">
                                <?php $__currentLoopData = $pluginMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pluginMenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <!-- Plugin Main Menu with Sub-items -->
                                    <div x-data="{ open: false }" class="plugin-menu" data-plugin="<?php echo e($pluginMenu['title']); ?>">
                                        <button @click="<?php echo e($pluginMenu['enabled'] ? 'open = !open' : ''); ?>"
                                                class="flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-lg focus:outline-none transition-colors duration-150
                                                       <?php echo e($pluginMenu['enabled']
                                                          ? 'text-gray-700 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'
                                                          : 'text-gray-400 cursor-not-allowed'); ?>"
                                                :class="{ 'bg-gray-50': open }"
                                                <?php echo e($pluginMenu['enabled'] ? '' : 'disabled'); ?>>
                                            <div class="flex items-center">
                                                <i class="<?php echo e($pluginMenu['icon']); ?> mr-3 <?php echo e($pluginMenu['enabled'] ? 'text-blue-600' : 'text-gray-300'); ?>"></i>
                                                <span class="font-semibold"><?php echo e($pluginMenu['title']); ?></span>
                                                <?php if(!$pluginMenu['enabled']): ?>
                                                    <span class="ml-2 text-xs bg-gray-200 text-gray-500 px-2 py-0.5 rounded-full">Disabled</span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($pluginMenu['enabled']): ?>
                                                <i class="fas fa-chevron-down transition-transform duration-200 text-gray-400" :class="{ 'rotate-180': open }"></i>
                                            <?php endif; ?>
                                        </button>
                                        <?php if($pluginMenu['enabled']): ?>
                                            <div x-show="open"
                                                 x-transition:enter="transition ease-out duration-200"
                                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                                 x-transition:leave="transition ease-in duration-150"
                                                 x-transition:leave-start="opacity-100 transform translate-y-0"
                                                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                                                 class="ml-6 mt-1 space-y-1 border-l-2 border-gray-200 pl-3">
                                                <?php $__currentLoopData = $pluginMenu['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e($item['url']); ?>"
                                                       class="flex items-center px-3 py-2 text-sm text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150">
                                                        <i class="<?php echo e($item['icon'] ?? 'fas fa-circle'); ?> mr-3 text-xs text-gray-400"></i>
                                                        <?php echo e($item['title']); ?>

                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Settings -->
                    <div class="pt-4 border-t border-gray-200">
                        <a href="<?php echo e(route('settings')); ?>"
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('settings') ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'); ?>">
                            <i class="fas fa-cog mr-3"></i>
                            Settings
                        </a>
                        <?php if(Route::has('plugins.manage')): ?>
                            <a href="<?php echo e(route('plugins.manage')); ?>"
                               class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100">
                                <i class="fas fa-plug mr-3"></i>
                                Plugin Manager
                            </a>
                        <?php else: ?>
                            <a href="/plugins"
                               class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100">
                                <i class="fas fa-plug mr-3"></i>
                                Plugin Manager
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Sidebar Overlay for Mobile -->
        <div x-show="sidebarOpen"
             @click="sidebarOpen = false"
             class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"></div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Top Navigation -->
            <nav class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button @click="sidebarOpen = true" class="lg:hidden mr-4">
                                <i class="fas fa-bars text-gray-500"></i>
                            </button>
                            <h2 class="text-xl font-semibold text-gray-900">
                                <?php echo $__env->yieldContent('page-title', 'Dashboard'); ?>
                            </h2>
                        </div>
                        <div class="flex items-center">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-user-circle mr-1"></i>
                                Head of Product
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Heading -->
            <?php if(isset($header)): ?>
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo e($header); ?>

                    </div>
                </header>
            <?php endif; ?>

            <!-- Page Content -->
            <main class="py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /Users/<USER>/Herd/team/resources/views/layouts/app.blade.php ENDPATH**/ ?>