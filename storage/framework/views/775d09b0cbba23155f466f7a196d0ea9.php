<!-- Number Card Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
    <div class="p-6 h-full flex flex-col justify-center">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <?php
                    $colorClasses = [
                        'blue' => 'bg-blue-100 text-blue-600',
                        'green' => 'bg-green-100 text-green-600',
                        'red' => 'bg-red-100 text-red-600',
                        'yellow' => 'bg-yellow-100 text-yellow-600',
                        'purple' => 'bg-purple-100 text-purple-600',
                        'gray' => 'bg-gray-100 text-gray-600'
                    ];
                    $colorClass = $colorClasses[$widget['data']['color']] ?? $colorClasses['blue'];
                ?>
                <div class="w-12 h-12 rounded-lg flex items-center justify-center <?php echo e($colorClass); ?>">
                    <i class="<?php echo e($widget['data']['icon']); ?> text-xl"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-500 mb-1"><?php echo e($widget['title']); ?></p>
                <div class="flex items-baseline">
                    <?php if($widget['data']['format'] === 'percentage'): ?>
                        <p class="text-3xl font-bold text-gray-900"><?php echo e($widget['data']['value']); ?>%</p>
                    <?php elseif($widget['data']['format'] === 'currency'): ?>
                        <p class="text-3xl font-bold text-gray-900">$<?php echo e(number_format($widget['data']['value'])); ?></p>
                    <?php else: ?>
                        <p class="text-3xl font-bold text-gray-900"><?php echo e(number_format($widget['data']['value'])); ?></p>
                    <?php endif; ?>
                </div>
                <?php if(isset($widget['data']['change'])): ?>
                    <div class="mt-2 flex items-center">
                        <?php if($widget['data']['change'] > 0): ?>
                            <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                            <span class="text-sm text-green-600">+<?php echo e($widget['data']['change']); ?>%</span>
                        <?php elseif($widget['data']['change'] < 0): ?>
                            <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                            <span class="text-sm text-red-600"><?php echo e($widget['data']['change']); ?>%</span>
                        <?php else: ?>
                            <i class="fas fa-minus text-gray-500 text-sm mr-1"></i>
                            <span class="text-sm text-gray-600">No change</span>
                        <?php endif; ?>
                        <span class="text-sm text-gray-500 ml-1">from last period</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/widgets/number-card.blade.php ENDPATH**/ ?>