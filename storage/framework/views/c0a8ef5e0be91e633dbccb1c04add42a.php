<!-- Table Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
    <!-- Widget Header -->
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900"><?php echo e($widget['title']); ?></h3>
        <div class="flex items-center space-x-2">
            <?php if($widget['data']['searchable'] ?? true): ?>
                <div class="relative">
                    <input type="text" 
                           placeholder="Search tasks..." 
                           class="block w-48 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            <?php endif; ?>
            <?php if($widget['data']['exportable'] ?? true): ?>
                <button class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Table Content -->
    <div class="flex-1 overflow-hidden">
        <div class="overflow-x-auto h-full">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 sticky top-0">
                    <tr>
                        <?php $__currentLoopData = $widget['data']['columns']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($column['visible']): ?>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-1">
                                        <span><?php echo e($column['label']); ?></span>
                                        <?php if($column['sortable']): ?>
                                            <i class="fas fa-sort text-gray-400 cursor-pointer hover:text-gray-600"></i>
                                        <?php endif; ?>
                                    </div>
                                </th>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $widget['data']['tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <?php $__currentLoopData = $widget['data']['columns']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($column['visible']): ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if($column['key'] === 'name'): ?>
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo e($task['name']); ?></div>
                                                    <?php if(!empty($task['description'])): ?>
                                                        <div class="text-sm text-gray-500"><?php echo e(Str::limit($task['description'], 60)); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php elseif($column['key'] === 'status'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                  style="background-color: <?php echo e($task['status']['color']); ?>20; color: <?php echo e($task['status']['color']); ?>;">
                                                <?php echo e($task['status']['status']); ?>

                                            </span>
                                        <?php elseif($column['key'] === 'assignee'): ?>
                                            <?php if(!empty($task['assignees'])): ?>
                                                <div class="flex items-center">
                                                    <?php if($task['assignees'][0]['profilePicture']): ?>
                                                        <img class="h-6 w-6 rounded-full mr-2" 
                                                             src="<?php echo e($task['assignees'][0]['profilePicture']); ?>" 
                                                             alt="<?php echo e($task['assignees'][0]['username']); ?>">
                                                    <?php else: ?>
                                                        <div class="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center mr-2">
                                                            <span class="text-xs text-gray-600"><?php echo e(substr($task['assignees'][0]['username'], 0, 1)); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <span class="text-sm text-gray-900"><?php echo e($task['assignees'][0]['username']); ?></span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-sm text-gray-500">Unassigned</span>
                                            <?php endif; ?>
                                        <?php elseif($column['key'] === 'due_date'): ?>
                                            <?php if($task['due_date']): ?>
                                                <?php
                                                    $dueDate = \Carbon\Carbon::createFromTimestamp($task['due_date'] / 1000);
                                                    $isOverdue = $dueDate->isPast();
                                                ?>
                                                <span class="text-sm <?php echo e($isOverdue ? 'text-red-600' : 'text-gray-900'); ?>">
                                                    <?php echo e($dueDate->format('M j, Y')); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="text-sm text-gray-500">-</span>
                                            <?php endif; ?>
                                        <?php elseif($column['key'] === 'priority'): ?>
                                            <?php if($task['priority']['priority']): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                                                      style="background-color: <?php echo e($task['priority']['color']); ?>20; color: <?php echo e($task['priority']['color']); ?>;">
                                                    <?php echo e(ucfirst($task['priority']['priority'])); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="text-sm text-gray-500">-</span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="<?php echo e(count(array_filter($widget['data']['columns'], fn($col) => $col['visible']))); ?>" 
                                class="px-6 py-12 text-center text-sm text-gray-500">
                                <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                                <div>No tasks found</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if(isset($widget['data']['pagination']) && $widget['data']['pagination']['total'] > $widget['data']['pagination']['per_page']): ?>
        <div class="px-6 py-3 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing <?php echo e($widget['data']['pagination']['per_page']); ?> of <?php echo e($widget['data']['pagination']['total']); ?> tasks
                </div>
                <div class="flex items-center space-x-2">
                    <button class="px-3 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                            <?php echo e($widget['data']['pagination']['page'] <= 1 ? 'disabled' : ''); ?>>
                        Previous
                    </button>
                    <span class="px-3 py-1 text-sm text-gray-700">
                        Page <?php echo e($widget['data']['pagination']['page']); ?>

                    </span>
                    <button class="px-3 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                            <?php echo e(!$widget['data']['pagination']['has_more'] ? 'disabled' : ''); ?>>
                        Next
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/widgets/table.blade.php ENDPATH**/ ?>