<?php $__env->startSection('page-title', 'ClickUp Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-cog text-blue-600 mr-3"></i>
                    ClickUp Settings
                </h1>
                <p class="mt-2 text-gray-600">Configure your ClickUp plugin settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('plugins.manage')); ?>"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plugin Manager
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <p class="text-sm font-medium text-green-800"><?php echo e(session('success')); ?></p>
            </div>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div class="text-sm text-red-700">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <p><?php echo e($error); ?></p>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Plugin Settings -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200"
         x-data="clickUpSettings()"
         x-init="init()"
         data-saved-space-id="<?php echo e(old('space_id', $config['space_id'] ?? '')); ?>">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">ClickUp Settings</h3>
                <p class="text-sm text-gray-600">Configure your ClickUp integration settings</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v1.0.0
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                </span>
            </div>
        </div>
    </div>

    <form method="POST" action="<?php echo e(route('plugins.settings.update', 'ClickUp')); ?>" class="p-6">
        <?php echo csrf_field(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label for="clickup_api_token" class="block text-sm font-medium text-gray-700">
                    API Token <span class="text-red-500">*</span>
                </label>
                <input type="password"
                       id="clickup_api_token"
                       name="api_token"
                       value="<?php echo e(old('api_token', $config['api_token'] ?? 'pk_707692_VG30E6E0O5BPKGW98T1TWSZV70AC45XI')); ?>"
                       required
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">Your ClickUp API token</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_space_id" class="block text-sm font-medium text-gray-700">
                    Space ID <span class="text-red-500">*</span>
                </label>
                
                <!-- Dynamic Space Dropdown -->
                <div x-show="loadingSpaces" class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span class="text-sm text-gray-600">Loading spaces...</span>
                </div>
                
                <div x-show="!loadingSpaces && !spacesError && spaces.length > 0">
                    <select id="clickup_space_id"
                            name="space_id"
                            required
                            x-model="selectedSpaceId"
                            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Select a space...</option>
                        <template x-for="space in spaces" :key="space.id">
                            <option :value="space.id"
                                    x-text="`${space.name} (${space.team_name})`"></option>
                        </template>
                    </select>
                </div>
                
                <div x-show="!loadingSpaces && (spacesError || spaces.length === 0)">
                    <div x-show="spacesError" class="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span x-text="spacesError"></span>
                    </div>
                    <input type="text"
                           id="clickup_space_id_fallback"
                           name="space_id"
                           value="<?php echo e(old('space_id', $config['space_id'] ?? '')); ?>"
                           placeholder="Enter Space ID manually"
                           required
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                
                <p class="text-xs text-gray-500">ClickUp Space ID to manage (required)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_team_id" class="block text-sm font-medium text-gray-700">
                    Team ID
                </label>
                <input type="text"
                       id="clickup_team_id"
                       name="team_id"
                       value="<?php echo e(old('team_id', $config['team_id'] ?? '')); ?>"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">ClickUp team ID for additional filtering (optional)</p>
            </div>

            <div class="space-y-2">
                <label for="clickup_sync_interval" class="block text-sm font-medium text-gray-700">
                    Sync Interval (minutes)
                </label>
                <input type="number"
                       id="clickup_sync_interval"
                       name="sync_interval"
                       value="<?php echo e(old('sync_interval', $config['sync_interval'] ?? 30)); ?>"
                       min="1"
                       class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                <p class="text-xs text-gray-500">How often to sync task data</p>
            </div>

            <div class="space-y-2 md:col-span-2">
                <label class="flex items-center">
                    <input type="checkbox"
                           name="plugin_enabled"
                           value="1"
                           <?php echo e(($config['plugin_enabled'] ?? true) ? 'checked' : ''); ?>

                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Enable Plugin</span>
                </label>
                <p class="text-xs text-gray-500">Enable or disable the ClickUp plugin</p>
            </div>
        </div>

        <div class="mt-6 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <button type="button" 
                        @click="testConnection()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <div id="connection-status" class="text-sm"></div>
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
function clickUpSettings() {
    return {
        loadingSpaces: false,
        spaces: [],
        spacesError: null,
        selectedSpaceId: '',

        init() {
            // Set the saved space ID value from data attribute
            const container = document.querySelector('[data-saved-space-id]');
            if (container) {
                this.selectedSpaceId = container.getAttribute('data-saved-space-id') || '';
            }
            this.loadSpaces();
        },
        
        async loadSpaces() {
            this.loadingSpaces = true;
            this.spacesError = null;
            
            try {
                const response = await fetch('/api/plugins/clickup/spaces');
                const data = await response.json();
                
                if (data.success) {
                    this.spaces = data.spaces || [];
                    if (this.spaces.length === 0) {
                        this.spacesError = 'No spaces found. Please check your API token and permissions.';
                    }
                } else {
                    this.spacesError = data.message || 'Failed to load spaces from ClickUp API.';
                }
            } catch (error) {
                console.error('Error loading ClickUp spaces:', error);
                this.spacesError = 'Network error: Unable to connect to ClickUp API.';
            } finally {
                this.loadingSpaces = false;
            }
        },
        
        async testConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-600"></i> Testing...';
            
            try {
                const response = await fetch('/api/plugins/clickup/test-connection');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '<i class="fas fa-check-circle text-green-600"></i> Connection successful';
                } else {
                    statusDiv.innerHTML = '<i class="fas fa-exclamation-circle text-red-600"></i> ' + data.message;
                }
            } catch (error) {
                statusDiv.innerHTML = '<i class="fas fa-times-circle text-red-600"></i> Connection error';
            }
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
    }
}
</script>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/settings.blade.php ENDPATH**/ ?>