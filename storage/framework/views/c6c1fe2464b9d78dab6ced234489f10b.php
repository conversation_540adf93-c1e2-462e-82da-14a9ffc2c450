<?php $__env->startSection('page-title', 'Plugin Manager'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto" x-data="pluginManager()">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-plug text-blue-600 mr-3"></i>
                    Plugin Manager
                </h1>
                <p class="mt-2 text-gray-600">Manage, install, and configure your plugins</p>
            </div>
            <div class="flex items-center space-x-4">
                <button @click="showImportModal = true"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-upload mr-2"></i>
                    Import Plugin
                </button>
                <button @click="runAllMigrations()"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-database mr-2"></i>
                    Run All Migrations
                </button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <p class="text-sm font-medium text-green-800"><?php echo e(session('success')); ?></p>
            </div>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <div class="text-sm text-red-700">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <p><?php echo e($error); ?></p>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Plugin Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__currentLoopData = $pluginData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plugin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" data-plugin="<?php echo e($plugin['name']); ?>">
                <!-- Plugin Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900"><?php echo e($plugin['name']); ?></h3>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                v<?php echo e($plugin['version']); ?>

                            </span>
                            <!-- Toggle Switch -->
                            <div class="flex items-center">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           class="sr-only peer"
                                           <?php echo e($plugin['enabled'] ? 'checked' : ''); ?>

                                           @change="togglePlugin('<?php echo e($plugin['name']); ?>', $event.target.checked)">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                                <span class="ml-2 text-xs font-medium status-text <?php echo e($plugin['enabled'] ? 'text-green-600' : 'text-red-600'); ?>">
                                    <?php echo e($plugin['enabled'] ? 'Enabled' : 'Disabled'); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mt-1"><?php echo e($plugin['description']); ?></p>
                </div>

                <!-- Plugin Actions -->
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <?php if($plugin['has_settings']): ?>
                                <a href="<?php echo e(route('plugins.settings', $plugin['name'])); ?>" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <i class="fas fa-cog mr-1"></i>
                                    Settings
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <button @click="runPluginMigrations('<?php echo e($plugin['name']); ?>')"
                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-database mr-1"></i>
                                    Migrate
                                </button>
                                <button @click="exportPlugin('<?php echo e($plugin['name']); ?>')"
                                        class="text-gray-600 hover:text-gray-800 text-sm">
                                    <i class="fas fa-download mr-1"></i>
                                    Export
                                </button>
                            </div>
                            <button @click="uninstallPlugin('<?php echo e($plugin['name']); ?>')"
                                    class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash mr-1"></i>
                                Uninstall
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Plugin Status Indicator -->
                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center text-xs text-gray-500">
                        <div class="status-indicator w-2 h-2 rounded-full mr-2 <?php echo e($plugin['enabled'] ? 'bg-green-400' : 'bg-red-400'); ?>"></div>
                        <span class="status-description"><?php echo e($plugin['enabled'] ? 'Active and running' : 'Inactive'); ?></span>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Import Plugin Modal -->
    <div x-show="showImportModal" 
         class="fixed inset-0 z-50 overflow-y-auto"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showImportModal = false"></div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form method="POST" action="<?php echo e(route('plugins.import')); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                <i class="fas fa-upload text-blue-600"></i>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Import Plugin</h3>
                                <div class="mt-4">
                                    <label for="plugin_zip" class="block text-sm font-medium text-gray-700 mb-2">
                                        Select Plugin ZIP File
                                    </label>
                                    <input type="file" 
                                           id="plugin_zip" 
                                           name="plugin_zip" 
                                           accept=".zip"
                                           required
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                    <p class="mt-2 text-xs text-gray-500">Maximum file size: 10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" 
                                class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Import Plugin
                        </button>
                        <button type="button" 
                                @click="showImportModal = false"
                                class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function pluginManager() {
    return {
        showImportModal: false,
        
        async togglePlugin(pluginName, enable) {
            try {
                const response = await fetch(`/plugins/${pluginName}/toggle`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    this.showNotification(data.message, 'success');

                    // Update plugin status in UI without full reload
                    this.updatePluginStatus(pluginName, data.enabled);

                    // Update sidebar navigation dynamically (without page reload)
                    this.updateSidebarNavigationDynamic(pluginName, data.enabled);
                } else {
                    alert('Failed to toggle plugin: ' + data.message);
                    // Revert checkbox state
                    this.revertCheckboxState(pluginName, !enable);
                }
            } catch (error) {
                alert('Error toggling plugin: ' + error.message);
                // Revert checkbox state
                this.revertCheckboxState(pluginName, !enable);
            }
        },

        updatePluginStatus(pluginName, enabled) {
            // Update checkbox state
            const checkboxes = document.querySelectorAll(`[data-plugin="${pluginName}"] input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = enabled;
            });

            // Update status text
            const statusElements = document.querySelectorAll(`[data-plugin="${pluginName}"] .status-text`);
            statusElements.forEach(el => {
                el.textContent = enabled ? 'Enabled' : 'Disabled';
                el.className = enabled ? 'ml-2 text-xs font-medium status-text text-green-600' : 'ml-2 text-xs font-medium status-text text-red-600';
            });

            // Update status indicator
            const indicators = document.querySelectorAll(`[data-plugin="${pluginName}"] .status-indicator`);
            indicators.forEach(el => {
                el.className = enabled ? 'status-indicator w-2 h-2 rounded-full mr-2 bg-green-400' : 'status-indicator w-2 h-2 rounded-full mr-2 bg-red-400';
            });

            // Update status description
            const descriptions = document.querySelectorAll(`[data-plugin="${pluginName}"] .status-description`);
            descriptions.forEach(el => {
                el.textContent = enabled ? 'Active and running' : 'Inactive';
            });
        },

        revertCheckboxState(pluginName, originalState) {
            const checkboxes = document.querySelectorAll(`[data-plugin="${pluginName}"] input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = originalState;
            });
        },

        updateSidebarNavigation() {
            // Reload the page to update sidebar navigation (used for uninstall)
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        },

        updateSidebarNavigationDynamic(pluginName, enabled) {
            // Update plugin main menu in hierarchical structure
            const pluginMenus = document.querySelectorAll('.plugin-menu');
            pluginMenus.forEach(menu => {
                if (menu.getAttribute('data-plugin') === pluginName) {
                    if (enabled) {
                        // Show the plugin menu
                        menu.style.display = '';
                    } else {
                        // Hide the plugin menu completely
                        menu.style.display = 'none';
                    }
                }
            });

            // Also update any individual navigation items that might reference the plugin
            const navItems = document.querySelectorAll('.sidebar-nav a');
            navItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && href.includes(pluginName.toLowerCase())) {
                    const listItem = item.closest('li');
                    if (listItem) {
                        if (enabled) {
                            listItem.style.display = '';
                        } else {
                            listItem.style.display = 'none';
                        }
                    }
                }
            });
        },

        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
                type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
                type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
                'bg-blue-100 text-blue-800 border border-blue-200'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        },

        async uninstallPlugin(pluginName) {
            if (!confirm(`Are you sure you want to uninstall the ${pluginName} plugin? This action cannot be undone and will remove all plugin data.`)) {
                return;
            }

            try {
                const response = await fetch(`/plugins/${pluginName}/uninstall`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.showNotification(data.message, 'success');

                    // Remove plugin card from UI
                    const pluginCard = document.querySelector(`[data-plugin="${pluginName}"]`);
                    if (pluginCard) {
                        pluginCard.style.transition = 'opacity 0.3s ease-out';
                        pluginCard.style.opacity = '0';
                        setTimeout(() => {
                            pluginCard.remove();
                        }, 300);
                    }

                    // Update sidebar navigation
                    this.updateSidebarNavigation();
                } else {
                    alert('Failed to uninstall plugin: ' + data.message);
                }
            } catch (error) {
                alert('Error uninstalling plugin: ' + error.message);
            }
        },
        
        async exportPlugin(pluginName) {
            try {
                const response = await fetch(`/plugins/${pluginName}/export`);
                const data = await response.json();

                if (data.success) {
                    // Download the file
                    window.location.href = data.download_url;
                } else {
                    alert('Export failed: ' + data.message);
                }
            } catch (error) {
                alert('Export error: ' + error.message);
            }
        },

        async runPluginMigrations(pluginName) {
            try {
                const response = await fetch(`/plugins/${pluginName}/migrations/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    alert('Migrations completed: ' + data.message);
                } else {
                    alert('Migration failed: ' + data.message);
                }
            } catch (error) {
                alert('Migration error: ' + error.message);
            }
        },

        async runAllMigrations() {
            if (!confirm('Are you sure you want to run migrations for all plugins?')) {
                return;
            }

            try {
                const response = await fetch('/plugins/migrations/run-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    alert('All migrations completed: ' + data.message);
                } else {
                    alert('Some migrations failed: ' + data.message);
                    console.log('Migration results:', data.results);
                }
            } catch (error) {
                alert('Migration error: ' + error.message);
            }
        }
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/resources/views/plugins/manage.blade.php ENDPATH**/ ?>