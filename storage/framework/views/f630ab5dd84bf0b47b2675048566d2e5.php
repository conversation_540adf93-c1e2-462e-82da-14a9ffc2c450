<?php $__env->startSection('page-content'); ?>
<!-- Task Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <?php
        $totalTasks = collect($tasks)->count();
        $completedTasks = collect($tasks)->where('status.status', 'completed')->count();
        $inProgressTasks = collect($tasks)->where('status.status', 'in progress')->count();
        $overdueTasks = collect($tasks)->filter(function($task) {
            return isset($task['due_date']) && strtotime($task['due_date']) < time() && $task['status']['status'] !== 'completed';
        })->count();
    ?>
    
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-tasks text-blue-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Tasks</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($totalTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($completedTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($inProgressTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Overdue</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e($overdueTasks); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Filter Tasks</h3>
    </div>
    <div class="p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Statuses</option>
                    <option value="open" <?php echo e(($filters['status'] ?? '') === 'open' ? 'selected' : ''); ?>>Open</option>
                    <option value="in_progress" <?php echo e(($filters['status'] ?? '') === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                    <option value="completed" <?php echo e(($filters['status'] ?? '') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                </select>
            </div>
            
            <div>
                <label for="list_id" class="block text-sm font-medium text-gray-700">List</label>
                <select name="list_id" id="list_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Lists</option>
                    <?php $__currentLoopData = $lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($list['id']); ?>" <?php echo e(($filters['list_id'] ?? '') === $list['id'] ? 'selected' : ''); ?>>
                            <?php echo e($list['name']); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div>
                <label for="assignee" class="block text-sm font-medium text-gray-700">Assignee</label>
                <input type="text" name="assignee" id="assignee" value="<?php echo e($filters['assignee'] ?? ''); ?>" 
                       placeholder="Assignee name"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-150 ease-in-out">
                    <i class="fas fa-filter mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Tasks List -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Tasks (<?php echo e(count($tasks)); ?>)</h3>
    </div>
    <div class="overflow-hidden">
        <?php if(count($tasks) > 0): ?>
            <ul class="divide-y divide-gray-200">
                <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="px-6 py-4 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="flex-shrink-0">
                                    <?php
                                        $statusColor = match($task['status']['status'] ?? 'open') {
                                            'completed' => 'text-green-600',
                                            'in progress' => 'text-blue-600',
                                            'overdue' => 'text-red-600',
                                            default => 'text-gray-600'
                                        };
                                    ?>
                                    <i class="fas fa-circle <?php echo e($statusColor); ?>"></i>
                                </div>
                                <div class="ml-4 min-w-0 flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">
                                            <?php echo e($task['name'] ?? 'Untitled Task'); ?>

                                        </h4>
                                        <?php if(isset($task['priority']) && $task['priority']['priority']): ?>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php echo e($task['priority']['priority'] === 'urgent' ? 'bg-red-100 text-red-800' : 
                                                   ($task['priority']['priority'] === 'high' ? 'bg-orange-100 text-orange-800' : 
                                                   ($task['priority']['priority'] === 'normal' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'))); ?>">
                                                <?php echo e(ucfirst($task['priority']['priority'])); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                        <span><?php echo e($task['status']['status'] ?? 'No status'); ?></span>
                                        <?php if(isset($task['due_date'])): ?>
                                            <span class="mx-2">•</span>
                                            <span>Due: <?php echo e(date('M j, Y', strtotime($task['due_date']))); ?></span>
                                        <?php endif; ?>
                                        <?php if(isset($task['assignees']) && count($task['assignees']) > 0): ?>
                                            <span class="mx-2">•</span>
                                            <span><?php echo e($task['assignees'][0]['username'] ?? 'Unassigned'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <?php if(isset($task['url'])): ?>
                                    <a href="<?php echo e($task['url']); ?>" target="_blank" 
                                       class="text-blue-600 hover:text-blue-500">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        <?php else: ?>
            <div class="px-6 py-12 text-center">
                <i class="fas fa-tasks text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                <p class="text-gray-500">
                    <?php if(isset($error)): ?>
                        There was an error loading tasks. Please check your ClickUp configuration.
                    <?php else: ?>
                        No tasks match your current filters.
                    <?php endif; ?>
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('clickup::layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/tasks.blade.php ENDPATH**/ ?>