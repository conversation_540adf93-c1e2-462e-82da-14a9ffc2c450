<?php $__env->startSection('page-title', 'Page Builder - ' . $page->name); ?>

<?php $__env->startSection('content'); ?>
<div class="h-screen flex flex-col bg-gray-50" x-data="pageBuilder(<?php echo e($page->id); ?>)">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="/clickup/settings" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-xl font-semibold text-gray-900"><?php echo e($page->name); ?></h1>
                    <p class="text-sm text-gray-500">Page Builder</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="previewPage()" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-eye mr-2"></i>
                    Preview
                </button>
                <button @click="savePage()" 
                        :disabled="saving"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50">
                    <i class="fas fa-save mr-2"></i>
                    <span x-text="saving ? 'Saving...' : 'Save'"></span>
                </button>
                <button @click="publishPage()" 
                        :disabled="publishing"
                        :class="pageData.is_published ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white disabled:opacity-50">
                    <i :class="pageData.is_published ? 'fas fa-check' : 'fas fa-rocket'" class="mr-2"></i>
                    <span x-text="publishing ? 'Publishing...' : (pageData.is_published ? 'Published' : 'Publish')"></span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
        <!-- Sidebar - Widget Library -->
        <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Widget Library</h3>
                
                <!-- Data Sources -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Data Sources</h4>
                    <div class="space-y-2">
                        <template x-for="source in dataSources" :key="source.id">
                            <div class="p-3 border border-gray-200 rounded-lg bg-gray-50">
                                <div class="text-sm font-medium text-gray-900" x-text="source.list_name"></div>
                                <div class="text-xs text-gray-500" x-text="`${source.space_name} > ${source.folder_name}`"></div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Widget Types -->
                <div class="space-y-4">
                    <template x-for="(category, categoryName) in groupedWidgetTypes" :key="categoryName">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2 capitalize" x-text="categoryName + ' Widgets'"></h4>
                            <div class="space-y-2">
                                <template x-for="widget in category" :key="widget.type">
                                    <div @click="startAddingWidget(widget)" 
                                         class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i :class="widget.icon" class="text-blue-600"></i>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900" x-text="widget.name"></div>
                                                <div class="text-xs text-gray-500" x-text="widget.description"></div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="flex-1 overflow-auto p-6">
            <div class="max-w-7xl mx-auto">
                <!-- Grid Container -->
                <div class="grid grid-cols-12 gap-4 min-h-96" id="canvas-grid">
                    <!-- Widgets will be rendered here -->
                    <template x-for="widget in widgets" :key="widget.id">
                        <div :class="`col-span-${widget.position.width} row-span-${widget.position.height}`"
                             :style="`grid-column-start: ${widget.position.x + 1}; grid-row-start: ${widget.position.y + 1};`"
                             class="relative group">
                            
                            <!-- Widget Container -->
                            <div class="h-full bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                                <!-- Widget Header -->
                                <div class="flex items-center justify-between p-3 border-b border-gray-100">
                                    <h4 class="text-sm font-medium text-gray-900" x-text="widget.title"></h4>
                                    <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <button @click="editWidget(widget)" 
                                                class="p-1 text-gray-400 hover:text-gray-600">
                                            <i class="fas fa-edit text-xs"></i>
                                        </button>
                                        <button @click="removeWidget(widget.id)" 
                                                class="p-1 text-gray-400 hover:text-red-600">
                                            <i class="fas fa-trash text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Widget Content -->
                                <div class="p-3">
                                    <div x-show="widget.type === 'table'">
                                        <div class="text-sm text-gray-500">Table Widget</div>
                                        <div class="text-xs text-gray-400">Data from: <span x-text="getDataSourceName(widget.data_source_id)"></span></div>
                                    </div>
                                    
                                    <div x-show="widget.type === 'number_card'">
                                        <div class="text-2xl font-bold text-blue-600">42</div>
                                        <div class="text-xs text-gray-400">Sample metric</div>
                                    </div>
                                    
                                    <div x-show="widget.type === 'progress_card'">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <div class="text-xs text-gray-500">65% Complete</div>
                                    </div>
                                    
                                    <div x-show="widget.type === 'status_chart'">
                                        <div class="flex items-center justify-center h-16">
                                            <i class="fas fa-chart-pie text-2xl text-gray-400"></i>
                                        </div>
                                        <div class="text-xs text-gray-400 text-center">Status Chart</div>
                                    </div>
                                    
                                    <div x-show="widget.type === 'timeline_card'">
                                        <div class="space-y-1">
                                            <div class="text-xs text-gray-500">Upcoming: 8</div>
                                            <div class="text-xs text-gray-500">Overdue: 3</div>
                                        </div>
                                    </div>
                                    
                                    <div x-show="widget.type === 'assignee_chart'">
                                        <div class="flex items-center justify-center h-16">
                                            <i class="fas fa-chart-bar text-2xl text-gray-400"></i>
                                        </div>
                                        <div class="text-xs text-gray-400 text-center">Assignee Chart</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    
                    <!-- Empty State -->
                    <div x-show="widgets.length === 0" class="col-span-12 flex items-center justify-center h-64 border-2 border-dashed border-gray-300 rounded-lg">
                        <div class="text-center">
                            <i class="fas fa-plus text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Page</h3>
                            <p class="text-gray-500 mb-4">Drag widgets from the sidebar to create your custom dashboard</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget Configuration Modal -->
    <div x-show="showWidgetModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        
        <div x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Configure Widget</h3>
            </div>
            
            <div class="px-6 py-4">
                <form @submit.prevent="saveWidget()">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Widget Title</label>
                            <input type="text" x-model="widgetForm.title" 
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Data Source</label>
                            <select x-model="widgetForm.data_source_id" 
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select data source...</option>
                                <template x-for="source in dataSources" :key="source.id">
                                    <option :value="source.id" x-text="`${source.list_name} (${source.space_name})`"></option>
                                </template>
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Width (columns)</label>
                                <select x-model="widgetForm.position.width" 
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="3">3 columns</option>
                                    <option value="4">4 columns</option>
                                    <option value="6">6 columns</option>
                                    <option value="8">8 columns</option>
                                    <option value="12">12 columns</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Height</label>
                                <select x-model="widgetForm.position.height" 
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="1">Small</option>
                                    <option value="2">Medium</option>
                                    <option value="3">Large</option>
                                    <option value="4">Extra Large</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button @click="closeWidgetModal()" 
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </button>
                <button @click="saveWidget()" 
                        class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    Add Widget
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function pageBuilder(pageId) {
    return {
        pageId: pageId,
        pageData: {},
        widgets: [],
        dataSources: [],
        widgetTypes: {},
        groupedWidgetTypes: {},
        showWidgetModal: false,
        saving: false,
        publishing: false,
        widgetForm: {
            type: '',
            title: '',
            data_source_id: '',
            position: {
                x: 0,
                y: 0,
                width: 6,
                height: 2
            },
            config: {}
        },

        init() {
            this.loadBuilderData();
        },

        async loadBuilderData() {
            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/data');
                const data = await response.json();

                if (data.success) {
                    this.pageData = data.data.page;
                    this.widgets = data.data.widgets || [];
                    this.dataSources = data.data.data_sources || [];
                    this.widgetTypes = data.data.widget_types || {};
                    this.groupedWidgetTypes = this.groupWidgetTypes(this.widgetTypes);

                    console.log('Loaded data sources:', this.dataSources);
                    console.log('Page data:', this.pageData);
                    console.log('Widgets:', this.widgets);
                } else {
                    console.error('Failed to load builder data:', data.message);
                    this.showError('Failed to load page data: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to load builder data:', error);
                this.showError('Failed to load page data. Please refresh the page.');
            }
        },

        groupWidgetTypes(types) {
            const grouped = {};
            Object.entries(types).forEach(([key, widget]) => {
                const category = widget.category || 'other';
                if (!grouped[category]) {
                    grouped[category] = [];
                }
                grouped[category].push({ ...widget, type: key });
            });
            return grouped;
        },

        startAddingWidget(widgetType) {
            this.widgetForm = {
                type: widgetType.type,
                title: widgetType.name,
                data_source_id: '',
                position: {
                    x: 0,
                    y: this.getNextAvailableRow(),
                    width: 6,
                    height: 2
                },
                config: {}
            };
            this.showWidgetModal = true;
        },

        getNextAvailableRow() {
            if (this.widgets.length === 0) return 0;
            const maxY = Math.max(...this.widgets.map(w => w.position.y + w.position.height));
            return maxY;
        },

        async saveWidget() {
            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/widgets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.widgetForm)
                });

                const data = await response.json();

                if (data.success) {
                    this.widgets.push(data.widget);
                    this.closeWidgetModal();
                    this.showSuccess('Widget added successfully!');
                } else {
                    this.showError('Failed to add widget: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to save widget:', error);
                this.showError('Failed to save widget. Please try again.');
            }
        },

        async removeWidget(widgetId) {
            if (!confirm('Are you sure you want to remove this widget?')) return;

            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/widgets/' + widgetId, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.widgets = this.widgets.filter(w => w.id !== widgetId);
                    this.showSuccess('Widget removed successfully!');
                } else {
                    this.showError('Failed to remove widget: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to remove widget:', error);
                this.showError('Failed to remove widget. Please try again.');
            }
        },

        closeWidgetModal() {
            this.showWidgetModal = false;
        },

        getDataSourceName(dataSourceId) {
            const source = this.dataSources.find(s => s.id === dataSourceId);
            return source ? source.list_name : 'Unknown';
        },

        async savePage() {
            this.saving = true;
            // Implementation for saving page layout
            setTimeout(() => {
                this.saving = false;
            }, 1000);
        },

        async publishPage() {
            this.publishing = true;
            try {
                const endpoint = this.pageData.is_published ? 'unpublish' : 'publish';
                const response = await fetch(`/api/plugins/clickup/builder/${this.pageId}/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    this.pageData.is_published = !this.pageData.is_published;
                }
            } catch (error) {
                console.error('Failed to publish page:', error);
            } finally {
                this.publishing = false;
            }
        },

        previewPage() {
            if (this.pageData.slug) {
                // Open the actual page if it has a slug
                window.open('/clickup/pages/' + this.pageData.slug, '_blank');
            } else {
                // For pages without slugs, show a message
                this.showError('Please save and publish the page first to enable preview.');
            }
        },

        showError(message) {
            // Simple error display - could be enhanced with a toast system
            alert('Error: ' + message);
        },

        showSuccess(message) {
            // Simple success display - could be enhanced with a toast system
            alert('Success: ' + message);
        },


    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('clickup::layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/page-builder.blade.php ENDPATH**/ ?>