<?php $__env->startSection('page-title', 'Page Builder - ' . $page->name); ?>

<?php $__env->startSection('content'); ?>
<style>
/* Grid System Styles */
.page-builder-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 16px;
    min-height: 600px;
    background-image:
        linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: calc(100% / 12) 40px;
    padding: 20px;
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    position: relative;
    transition: all 0.2s ease;
}

.page-builder-grid.show-grid {
    background-image:
        linear-gradient(to right, rgba(59, 130, 246, 0.3) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(59, 130, 246, 0.3) 1px, transparent 1px);
}

.page-builder-grid.drag-over {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
}

/* Widget Container Styles */
.widget-container {
    position: relative;
    background: white;
    border: 2px solid transparent;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: move;
    transition: all 0.2s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
}

.widget-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.widget-container.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    z-index: 10;
}

.widget-container.selected::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #3b82f6;
    border-radius: 10px;
    pointer-events: none;
    animation: selection-pulse 2s infinite;
}

@keyframes selection-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.widget-container.dragging {
    opacity: 0.7;
    transform: rotate(2deg);
    z-index: 1000;
}

/* Resize Handles */
.resize-handle {
    position: absolute;
    background: #3b82f6;
    border: 2px solid white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
}

.widget-container.selected .resize-handle {
    opacity: 1;
}

.resize-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
.resize-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
.resize-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
.resize-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }
.resize-handle.n { top: -6px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.resize-handle.s { bottom: -6px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.resize-handle.w { top: 50%; left: -6px; transform: translateY(-50%); cursor: w-resize; }
.resize-handle.e { top: 50%; right: -6px; transform: translateY(-50%); cursor: e-resize; }

/* Widget Library Styles */
.widget-library {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

.widget-library-item {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    cursor: grab;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.widget-library-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.widget-library-item:active {
    cursor: grabbing;
}

.widget-library-item.dragging {
    opacity: 0.5;
}

/* Drop Zone Indicators */
.drop-zone {
    position: absolute;
    border: 2px dashed #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 5;
}

.drop-zone.active {
    opacity: 1;
}

/* Grid Position Indicator */
.grid-position-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
}

.widget-container:hover .grid-position-indicator,
.widget-container.selected .grid-position-indicator {
    opacity: 1;
}

/* Responsive Grid Breakpoints */
@media (max-width: 768px) {
    .page-builder-grid {
        grid-template-columns: repeat(6, 1fr);
        background-size: calc(100% / 6) 40px;
    }
}

@media (max-width: 480px) {
    .page-builder-grid {
        grid-template-columns: repeat(4, 1fr);
        background-size: calc(100% / 4) 40px;
    }
}

/* Animation for widget placement */
@keyframes widget-place {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

.widget-container.just-placed {
    animation: widget-place 0.3s ease-out;
}

/* Touch device support */
@media (hover: none) and (pointer: coarse) {
    .widget-container .resize-handle {
        opacity: 1;
        width: 16px;
        height: 16px;
    }

    .widget-library-item {
        padding: 16px 12px;
    }

    .grid-position-indicator {
        opacity: 1;
    }
}
</style>

<div class="h-screen flex flex-col bg-gray-50" x-data="pageBuilder(<?php echo e($page->id); ?>)">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="/clickup/settings" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-xl font-semibold text-gray-900"><?php echo e($page->name); ?></h1>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>Page Builder</span>
                        <span class="flex items-center">
                            <i class="fas fa-puzzle-piece mr-1"></i>
                            <span x-text="widgets.length"></span> widgets
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Undo/Redo Buttons -->
                <div class="flex items-center space-x-1 border border-gray-300 rounded-md">
                    <button @click="undo()"
                            :disabled="historyIndex <= 0"
                            class="px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Undo (Ctrl+Z)">
                        <i class="fas fa-undo"></i>
                    </button>
                    <div class="w-px h-4 bg-gray-300"></div>
                    <button @click="redo()"
                            :disabled="historyIndex >= history.length - 1"
                            class="px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Redo (Ctrl+Y)">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>

                <!-- Grid Toggle -->
                <button @click="showGrid = !showGrid"
                        :class="showGrid ? 'bg-blue-50 border-blue-300 text-blue-700' : 'bg-white border-gray-300 text-gray-700'"
                        class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md hover:bg-gray-50"
                        title="Toggle Grid Lines">
                    <i class="fas fa-th mr-2"></i>
                    Grid
                </button>

                <button @click="previewPage()"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-eye mr-2"></i>
                    Preview
                </button>
                <button @click="savePage()" 
                        :disabled="saving"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50">
                    <i class="fas fa-save mr-2"></i>
                    <span x-text="saving ? 'Saving...' : 'Save'"></span>
                </button>
                <button @click="publishPage()" 
                        :disabled="publishing"
                        :class="pageData.is_published ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white disabled:opacity-50">
                    <i :class="pageData.is_published ? 'fas fa-check' : 'fas fa-rocket'" class="mr-2"></i>
                    <span x-text="publishing ? 'Publishing...' : (pageData.is_published ? 'Published' : 'Publish')"></span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
        <!-- Sidebar - Widget Library -->
        <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Widget Library</h3>

                <!-- Data Sources -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Data Sources</h4>
                    <div class="space-y-2">
                        <template x-for="source in dataSources" :key="source.id">
                            <div class="p-3 border border-gray-200 rounded-lg bg-gray-50">
                                <div class="text-sm font-medium text-gray-900" x-text="source.name || source.list_name"></div>
                                <div class="text-xs text-gray-500" x-text="source.description || `${source.space_name} > ${source.folder_name}`"></div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Widget Types -->
                <div class="widget-library">
                    <template x-for="(category, categoryName) in groupedWidgetTypes" :key="categoryName">
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2 capitalize" x-text="categoryName + ' Widgets'"></h4>
                            <div class="space-y-2">
                                <template x-for="widget in category" :key="widget.type">
                                    <div class="widget-library-item"
                                         draggable="true"
                                         @dragstart="startDragNewWidget($event, widget)"
                                         @dragend="endDragWidget($event)">
                                        <div class="flex items-center w-full">
                                            <div class="flex-shrink-0">
                                                <i :class="widget.icon" class="text-blue-600"></i>
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="text-sm font-medium text-gray-900" x-text="widget.name"></div>
                                                <div class="text-xs text-gray-500" x-text="widget.description"></div>
                                            </div>
                                            <div class="text-xs text-gray-400">
                                                <i class="fas fa-grip-vertical"></i>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="flex-1 overflow-auto p-6">
            <div class="max-w-7xl mx-auto">
                <!-- Grid Container -->
                <div class="page-builder-grid"
                     :class="{ 'show-grid': showGrid }"
                     id="canvas-grid"
                     @dragover.prevent="handleDragOver($event)"
                     @drop.prevent="handleDrop($event)"
                     @dragleave="handleDragLeave($event)">

                    <!-- Drop Zone Indicator -->
                    <div class="drop-zone" x-ref="dropZone"></div>

                    <!-- Widgets -->
                    <template x-for="widget in widgets" :key="widget.id">
                        <div class="widget-container"
                             :class="{ 'selected': selectedWidget?.id === widget.id, 'just-placed': widget.justPlaced }"
                             :style="getWidgetGridStyle(widget)"
                             @click="selectWidget(widget)"
                             @mousedown="startDragWidget($event, widget)"
                             draggable="true"
                             @dragstart="startDragExistingWidget($event, widget)"
                             @dragend="endDragWidget($event)">

                            <!-- Grid Position Indicator -->
                            <div class="grid-position-indicator" x-text="getGridPositionText(widget)"></div>

                            <!-- Resize Handles -->
                            <template x-if="selectedWidget?.id === widget.id">
                                <div>
                                    <div class="resize-handle nw" @mousedown.stop="startResize($event, widget, 'nw')"></div>
                                    <div class="resize-handle ne" @mousedown.stop="startResize($event, widget, 'ne')"></div>
                                    <div class="resize-handle sw" @mousedown.stop="startResize($event, widget, 'sw')"></div>
                                    <div class="resize-handle se" @mousedown.stop="startResize($event, widget, 'se')"></div>
                                    <div class="resize-handle n" @mousedown.stop="startResize($event, widget, 'n')"></div>
                                    <div class="resize-handle s" @mousedown.stop="startResize($event, widget, 's')"></div>
                                    <div class="resize-handle w" @mousedown.stop="startResize($event, widget, 'w')"></div>
                                    <div class="resize-handle e" @mousedown.stop="startResize($event, widget, 'e')"></div>
                                </div>
                            </template>

                            <!-- Widget Header -->
                            <div class="flex items-center justify-between p-3 border-b border-gray-100 bg-gray-50 rounded-t-lg">
                                <h4 class="text-sm font-medium text-gray-900" x-text="widget.title"></h4>
                                <div class="flex items-center space-x-1">
                                    <button @click.stop="editWidget(widget)"
                                            class="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <i class="fas fa-edit text-xs"></i>
                                    </button>
                                    <button @click.stop="duplicateWidget(widget)"
                                            class="p-1 text-gray-400 hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <i class="fas fa-copy text-xs"></i>
                                    </button>
                                    <button @click.stop="removeWidget(widget.id)"
                                            class="p-1 text-gray-400 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <i class="fas fa-trash text-xs"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Widget Content -->
                            <div class="p-3 flex-1 overflow-hidden">
                                <div x-show="widget.type === 'table'">
                                    <div class="text-sm text-gray-500 mb-1">Table Widget</div>
                                    <div class="text-xs text-gray-400">Data from: <span x-text="getDataSourceName(widget.data_source_id)"></span></div>
                                    <div class="mt-2 space-y-1">
                                        <div class="h-2 bg-gray-200 rounded"></div>
                                        <div class="h-2 bg-gray-200 rounded w-3/4"></div>
                                        <div class="h-2 bg-gray-200 rounded w-1/2"></div>
                                    </div>
                                </div>

                                <div x-show="widget.type === 'number_card'">
                                    <div class="text-center">
                                        <div class="text-3xl font-bold text-blue-600 mb-1">42</div>
                                        <div class="text-xs text-gray-400">Sample Metric</div>
                                    </div>
                                </div>

                                <div x-show="widget.type === 'progress_card'">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-gray-900 mb-2">Progress</div>
                                        <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                                            <div class="bg-blue-600 h-3 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <div class="text-sm text-gray-500">65% Complete</div>
                                    </div>
                                </div>

                                <div x-show="widget.type === 'status_chart'">
                                    <div class="flex items-center justify-center h-20">
                                        <i class="fas fa-chart-pie text-3xl text-gray-400"></i>
                                    </div>
                                    <div class="text-xs text-gray-400 text-center">Status Distribution</div>
                                </div>

                                <div x-show="widget.type === 'timeline_card'">
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Upcoming:</span>
                                            <span class="font-medium">8</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Overdue:</span>
                                            <span class="font-medium text-red-600">3</span>
                                        </div>
                                    </div>
                                </div>

                                <div x-show="widget.type === 'assignee_chart'">
                                    <div class="flex items-center justify-center h-20">
                                        <i class="fas fa-chart-bar text-3xl text-gray-400"></i>
                                    </div>
                                    <div class="text-xs text-gray-400 text-center">Team Performance</div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- Empty State -->
                    <div x-show="widgets.length === 0"
                         class="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div class="text-center">
                            <i class="fas fa-mouse-pointer text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Dashboard</h3>
                            <p class="text-gray-500 mb-4">Drag widgets from the sidebar to create your custom page</p>
                            <div class="text-sm text-gray-400">
                                <i class="fas fa-info-circle mr-1"></i>
                                Widgets will snap to the 12-column grid
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget Configuration Modal -->
    <div x-show="showWidgetModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        
        <div x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Configure Widget</h3>
            </div>
            
            <div class="px-6 py-4">
                <form @submit.prevent="saveWidget()">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Widget Title</label>
                            <input type="text" x-model="widgetForm.title" 
                                   class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Data Source</label>
                            <select x-model="widgetForm.data_source_id" 
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select data source...</option>
                                <template x-for="source in dataSources" :key="source.id">
                                    <option :value="source.id" x-text="`${source.list_name} (${source.space_name})`"></option>
                                </template>
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Width (columns)</label>
                                <select x-model="widgetForm.position.width" 
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="3">3 columns</option>
                                    <option value="4">4 columns</option>
                                    <option value="6">6 columns</option>
                                    <option value="8">8 columns</option>
                                    <option value="12">12 columns</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Height</label>
                                <select x-model="widgetForm.position.height" 
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="1">Small</option>
                                    <option value="2">Medium</option>
                                    <option value="3">Large</option>
                                    <option value="4">Extra Large</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button @click="closeWidgetModal()" 
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </button>
                <button @click="saveWidget()" 
                        class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    Add Widget
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function pageBuilder(pageId) {
    return {
        pageId: pageId,
        pageData: {},
        widgets: [],
        dataSources: [],
        widgetTypes: {},
        groupedWidgetTypes: {},
        showWidgetModal: false,
        saving: false,
        publishing: false,

        // Drag and Drop State
        draggedWidget: null,
        draggedWidgetType: null,
        isDragging: false,
        selectedWidget: null,
        isResizing: false,
        resizeData: null,
        gridColumns: 12,

        // Undo/Redo State
        history: [],
        historyIndex: -1,

        // UI State
        showGrid: false,

        widgetForm: {
            type: '',
            title: '',
            data_source_id: '',
            position: {
                x: 0,
                y: 0,
                width: 6,
                height: 2
            },
            config: {}
        },

        init() {
            this.loadBuilderData();

            // Add keyboard event listener
            document.addEventListener('keydown', this.handleKeyDown.bind(this));

            // Add click outside handler to deselect widgets
            document.addEventListener('click', (event) => {
                if (!event.target.closest('.widget-container') && !event.target.closest('.widget-library-item')) {
                    this.selectedWidget = null;
                }
            });

            // Initialize history
            this.saveToHistory();
        },

        async loadBuilderData() {
            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/data');
                const data = await response.json();

                if (data.success) {
                    this.pageData = data.data.page;
                    this.widgets = data.data.widgets || [];
                    this.dataSources = data.data.data_sources || [];
                    this.widgetTypes = data.data.widget_types || {};
                    this.groupedWidgetTypes = this.groupWidgetTypes(this.widgetTypes);

                    console.log('Loaded data sources:', this.dataSources);
                    console.log('Page data:', this.pageData);
                    console.log('Widgets:', this.widgets);
                } else {
                    console.error('Failed to load builder data:', data.message);
                    this.showError('Failed to load page data: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to load builder data:', error);
                this.showError('Failed to load page data. Please refresh the page.');
            }
        },

        groupWidgetTypes(types) {
            const grouped = {};
            Object.entries(types).forEach(([key, widget]) => {
                const category = widget.category || 'other';
                if (!grouped[category]) {
                    grouped[category] = [];
                }
                grouped[category].push({ ...widget, type: key });
            });
            return grouped;
        },

        startAddingWidget(widgetType) {
            this.widgetForm = {
                type: widgetType.type,
                title: widgetType.name,
                data_source_id: '',
                position: {
                    x: 0,
                    y: this.getNextAvailableRow(),
                    width: 6,
                    height: 2
                },
                config: {}
            };
            this.showWidgetModal = true;
        },

        getNextAvailableRow() {
            if (this.widgets.length === 0) return 0;
            const maxY = Math.max(...this.widgets.map(w => w.position.y + w.position.height));
            return maxY;
        },

        async saveWidget() {
            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/widgets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.widgetForm)
                });

                const data = await response.json();

                if (data.success) {
                    this.widgets.push(data.widget);
                    this.closeWidgetModal();
                    this.showSuccess('Widget added successfully!');
                } else {
                    this.showError('Failed to add widget: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to save widget:', error);
                this.showError('Failed to save widget. Please try again.');
            }
        },

        async removeWidget(widgetId) {
            if (!confirm('Are you sure you want to remove this widget?')) return;

            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/widgets/' + widgetId, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.widgets = this.widgets.filter(w => w.id !== widgetId);
                    this.showSuccess('Widget removed successfully!');
                } else {
                    this.showError('Failed to remove widget: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to remove widget:', error);
                this.showError('Failed to remove widget. Please try again.');
            }
        },

        closeWidgetModal() {
            this.showWidgetModal = false;
        },

        getDataSourceName(dataSourceId) {
            const source = this.dataSources.find(s => s.id === dataSourceId);
            return source ? source.list_name : 'Unknown';
        },

        async savePage() {
            this.saving = true;
            // Implementation for saving page layout
            setTimeout(() => {
                this.saving = false;
            }, 1000);
        },

        async publishPage() {
            this.publishing = true;
            try {
                const endpoint = this.pageData.is_published ? 'unpublish' : 'publish';
                const response = await fetch(`/api/plugins/clickup/builder/${this.pageId}/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    this.pageData.is_published = !this.pageData.is_published;
                }
            } catch (error) {
                console.error('Failed to publish page:', error);
            } finally {
                this.publishing = false;
            }
        },

        previewPage() {
            if (this.pageData.slug) {
                // Open the actual page if it has a slug
                window.open('/clickup/pages/' + this.pageData.slug, '_blank');
            } else {
                // For pages without slugs, show a message
                this.showError('Please save and publish the page first to enable preview.');
            }
        },

        showError(message) {
            // Simple error display - could be enhanced with a toast system
            alert('Error: ' + message);
        },

        showSuccess(message) {
            // Simple success display - could be enhanced with a toast system
            alert('Success: ' + message);
        },

        // Drag and Drop Methods
        startDragNewWidget(event, widgetType) {
            this.draggedWidgetType = widgetType;
            this.isDragging = true;

            // Set drag data
            event.dataTransfer.setData('text/plain', JSON.stringify({
                type: 'new-widget',
                widgetType: widgetType
            }));

            // Add dragging class to the library item
            event.target.classList.add('dragging');

            // Set drag effect
            event.dataTransfer.effectAllowed = 'copy';
        },

        startDragExistingWidget(event, widget) {
            this.draggedWidget = widget;
            this.isDragging = true;

            // Set drag data
            event.dataTransfer.setData('text/plain', JSON.stringify({
                type: 'existing-widget',
                widget: widget
            }));

            // Add dragging class
            event.target.classList.add('dragging');

            // Set drag effect
            event.dataTransfer.effectAllowed = 'move';
        },

        endDragWidget(event) {
            this.isDragging = false;
            this.draggedWidget = null;
            this.draggedWidgetType = null;

            // Remove dragging class
            event.target.classList.remove('dragging');

            // Hide drop zone
            this.hideDropZone();
        },

        handleDragOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';

            // Calculate grid position
            const gridPosition = this.calculateGridPosition(event.clientX, event.clientY);
            if (gridPosition) {
                this.showDropZone(gridPosition);
            }

            // Add drag-over class to grid
            event.currentTarget.classList.add('drag-over');
        },

        handleDragLeave(event) {
            // Only hide if leaving the grid container
            if (!event.currentTarget.contains(event.relatedTarget)) {
                event.currentTarget.classList.remove('drag-over');
                this.hideDropZone();
            }
        },

        async handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');

            try {
                const dragData = JSON.parse(event.dataTransfer.getData('text/plain'));
                const gridPosition = this.calculateGridPosition(event.clientX, event.clientY);

                if (!gridPosition) return;

                if (dragData.type === 'new-widget') {
                    await this.createWidgetAtPosition(dragData.widgetType, gridPosition);
                } else if (dragData.type === 'existing-widget') {
                    await this.moveWidgetToPosition(dragData.widget, gridPosition);
                }
            } catch (error) {
                console.error('Failed to handle drop:', error);
            } finally {
                this.hideDropZone();
            }
        },

        calculateGridPosition(clientX, clientY) {
            const grid = document.getElementById('canvas-grid');
            if (!grid) return null;

            const rect = grid.getBoundingClientRect();
            const x = clientX - rect.left - 20; // Account for padding
            const y = clientY - rect.top - 20;

            if (x < 0 || y < 0) return null;

            const cellWidth = (rect.width - 40) / this.gridColumns; // Account for padding and gaps
            const cellHeight = 40; // Fixed row height

            const gridX = Math.floor(x / cellWidth);
            const gridY = Math.floor(y / cellHeight);

            return {
                x: Math.max(0, Math.min(gridX, this.gridColumns - 1)),
                y: Math.max(0, gridY),
                width: 4, // Default width
                height: 2  // Default height
            };
        },

        showDropZone(position) {
            const dropZone = this.$refs.dropZone;
            if (!dropZone) return;

            const grid = document.getElementById('canvas-grid');
            const rect = grid.getBoundingClientRect();
            const cellWidth = (rect.width - 40) / this.gridColumns;
            const cellHeight = 40;

            dropZone.style.left = (20 + position.x * cellWidth) + 'px';
            dropZone.style.top = (20 + position.y * cellHeight) + 'px';
            dropZone.style.width = (position.width * cellWidth - 16) + 'px';
            dropZone.style.height = (position.height * cellHeight - 16) + 'px';
            dropZone.classList.add('active');
        },

        hideDropZone() {
            const dropZone = this.$refs.dropZone;
            if (dropZone) {
                dropZone.classList.remove('active');
            }
        },

        async createWidgetAtPosition(widgetType, position) {
            // Check for overlaps
            if (this.checkOverlap(position)) {
                position = this.findNextAvailablePosition(position);
            }

            const widgetData = {
                type: widgetType.type,
                title: widgetType.name,
                data_source_id: this.dataSources[0]?.id || '',
                position: position,
                config: {}
            };

            try {
                const response = await fetch('/api/plugins/clickup/builder/' + this.pageId + '/widgets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(widgetData)
                });

                const data = await response.json();

                if (data.success) {
                    data.widget.justPlaced = true;
                    this.widgets.push(data.widget);
                    this.selectWidget(data.widget);

                    // Remove just-placed class after animation
                    setTimeout(() => {
                        data.widget.justPlaced = false;
                    }, 300);

                    this.saveToHistory();
                } else {
                    this.showError('Failed to add widget: ' + data.message);
                }
            } catch (error) {
                console.error('Failed to create widget:', error);
                this.showError('Failed to create widget');
            }
        },

        async moveWidgetToPosition(widget, position) {
            // Check for overlaps (excluding the widget being moved)
            if (this.checkOverlap(position, widget.id)) {
                position = this.findNextAvailablePosition(position, widget.id);
            }

            const oldPosition = { ...widget.position };
            widget.position = position;

            try {
                await this.updateWidgetPosition(widget);
                this.saveToHistory();
            } catch (error) {
                // Revert position on error
                widget.position = oldPosition;
                this.showError('Failed to move widget');
            }
        },

        checkOverlap(position, excludeWidgetId = null) {
            return this.widgets.some(widget => {
                if (excludeWidgetId && widget.id === excludeWidgetId) return false;

                return !(position.x >= widget.position.x + widget.position.width ||
                        position.x + position.width <= widget.position.x ||
                        position.y >= widget.position.y + widget.position.height ||
                        position.y + position.height <= widget.position.y);
            });
        },

        findNextAvailablePosition(preferredPosition, excludeWidgetId = null) {
            let position = { ...preferredPosition };

            // Try to find a position in the same row first
            for (let x = 0; x <= this.gridColumns - position.width; x++) {
                position.x = x;
                if (!this.checkOverlap(position, excludeWidgetId)) {
                    return position;
                }
            }

            // If no space in current row, try next rows
            for (let y = position.y + 1; y < 100; y++) { // Arbitrary max rows
                for (let x = 0; x <= this.gridColumns - position.width; x++) {
                    position.x = x;
                    position.y = y;
                    if (!this.checkOverlap(position, excludeWidgetId)) {
                        return position;
                    }
                }
            }

            return preferredPosition; // Fallback
        },

        selectWidget(widget) {
            this.selectedWidget = widget;
        },

        getWidgetGridStyle(widget) {
            return {
                gridColumn: `${widget.position.x + 1} / span ${widget.position.width}`,
                gridRow: `${widget.position.y + 1} / span ${widget.position.height}`
            };
        },

        getGridPositionText(widget) {
            return `${widget.position.width}×${widget.position.height}`;
        },

        async updateWidgetPosition(widget) {
            const response = await fetch(`/api/plugins/clickup/builder/${this.pageId}/widgets/${widget.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    position: widget.position
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message);
            }
        },

        duplicateWidget(widget) {
            const newPosition = this.findNextAvailablePosition({
                x: widget.position.x,
                y: widget.position.y + widget.position.height,
                width: widget.position.width,
                height: widget.position.height
            });

            this.createWidgetAtPosition({
                type: widget.type,
                name: widget.title + ' (Copy)'
            }, newPosition);
        },

        saveToHistory() {
            // Simple history implementation
            const state = JSON.stringify(this.widgets.map(w => ({ ...w })));
            this.history = this.history.slice(0, this.historyIndex + 1);
            this.history.push(state);
            this.historyIndex++;

            // Limit history size
            if (this.history.length > 50) {
                this.history.shift();
                this.historyIndex--;
            }
        },

        undo() {
            if (this.historyIndex > 0) {
                this.historyIndex--;
                this.widgets = JSON.parse(this.history[this.historyIndex]);
            }
        },

        redo() {
            if (this.historyIndex < this.history.length - 1) {
                this.historyIndex++;
                this.widgets = JSON.parse(this.history[this.historyIndex]);
            }
        },

        // Resize Methods
        startResize(event, widget, direction) {
            event.preventDefault();
            event.stopPropagation();

            this.isResizing = true;
            this.resizeData = {
                widget: widget,
                direction: direction,
                startX: event.clientX,
                startY: event.clientY,
                startPosition: { ...widget.position }
            };

            // Add event listeners for mouse move and up
            document.addEventListener('mousemove', this.handleResize.bind(this));
            document.addEventListener('mouseup', this.endResize.bind(this));

            // Prevent text selection during resize
            document.body.style.userSelect = 'none';
        },

        handleResize(event) {
            if (!this.isResizing || !this.resizeData) return;

            const { widget, direction, startX, startY, startPosition } = this.resizeData;
            const deltaX = event.clientX - startX;
            const deltaY = event.clientY - startY;

            // Calculate grid cell size
            const grid = document.getElementById('canvas-grid');
            const rect = grid.getBoundingClientRect();
            const cellWidth = (rect.width - 40) / this.gridColumns;
            const cellHeight = 40;

            // Convert pixel deltas to grid units
            const gridDeltaX = Math.round(deltaX / cellWidth);
            const gridDeltaY = Math.round(deltaY / cellHeight);

            let newPosition = { ...startPosition };

            // Apply resize based on direction
            switch (direction) {
                case 'se': // Southeast - resize width and height
                    newPosition.width = Math.max(1, startPosition.width + gridDeltaX);
                    newPosition.height = Math.max(1, startPosition.height + gridDeltaY);
                    break;
                case 'sw': // Southwest - resize width and height, move x
                    const newWidth = Math.max(1, startPosition.width - gridDeltaX);
                    newPosition.x = startPosition.x + (startPosition.width - newWidth);
                    newPosition.width = newWidth;
                    newPosition.height = Math.max(1, startPosition.height + gridDeltaY);
                    break;
                case 'ne': // Northeast - resize width and height, move y
                    newPosition.width = Math.max(1, startPosition.width + gridDeltaX);
                    const newHeight = Math.max(1, startPosition.height - gridDeltaY);
                    newPosition.y = startPosition.y + (startPosition.height - newHeight);
                    newPosition.height = newHeight;
                    break;
                case 'nw': // Northwest - resize width and height, move x and y
                    const newWidthNW = Math.max(1, startPosition.width - gridDeltaX);
                    const newHeightNW = Math.max(1, startPosition.height - gridDeltaY);
                    newPosition.x = startPosition.x + (startPosition.width - newWidthNW);
                    newPosition.y = startPosition.y + (startPosition.height - newHeightNW);
                    newPosition.width = newWidthNW;
                    newPosition.height = newHeightNW;
                    break;
                case 'e': // East - resize width only
                    newPosition.width = Math.max(1, startPosition.width + gridDeltaX);
                    break;
                case 'w': // West - resize width, move x
                    const newWidthW = Math.max(1, startPosition.width - gridDeltaX);
                    newPosition.x = startPosition.x + (startPosition.width - newWidthW);
                    newPosition.width = newWidthW;
                    break;
                case 's': // South - resize height only
                    newPosition.height = Math.max(1, startPosition.height + gridDeltaY);
                    break;
                case 'n': // North - resize height, move y
                    const newHeightN = Math.max(1, startPosition.height - gridDeltaY);
                    newPosition.y = startPosition.y + (startPosition.height - newHeightN);
                    newPosition.height = newHeightN;
                    break;
            }

            // Ensure widget stays within grid bounds
            newPosition.x = Math.max(0, Math.min(newPosition.x, this.gridColumns - newPosition.width));
            newPosition.y = Math.max(0, newPosition.y);
            newPosition.width = Math.min(newPosition.width, this.gridColumns - newPosition.x);

            // Check for overlaps with other widgets
            if (!this.checkOverlap(newPosition, widget.id)) {
                widget.position = newPosition;
            }
        },

        endResize(event) {
            if (!this.isResizing) return;

            this.isResizing = false;

            // Remove event listeners
            document.removeEventListener('mousemove', this.handleResize.bind(this));
            document.removeEventListener('mouseup', this.endResize.bind(this));

            // Restore text selection
            document.body.style.userSelect = '';

            // Save the resize to backend
            if (this.resizeData) {
                this.updateWidgetPosition(this.resizeData.widget);
                this.saveToHistory();
            }

            this.resizeData = null;
        },

        // Keyboard shortcuts
        handleKeyDown(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'z':
                        event.preventDefault();
                        if (event.shiftKey) {
                            this.redo();
                        } else {
                            this.undo();
                        }
                        break;
                    case 'y':
                        event.preventDefault();
                        this.redo();
                        break;
                    case 'd':
                        event.preventDefault();
                        if (this.selectedWidget) {
                            this.duplicateWidget(this.selectedWidget);
                        }
                        break;
                }
            } else if (event.key === 'Delete' || event.key === 'Backspace') {
                if (this.selectedWidget) {
                    event.preventDefault();
                    this.removeWidget(this.selectedWidget.id);
                }
            } else if (event.key === 'Escape') {
                this.selectedWidget = null;
            }
        },


    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('clickup::layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/team/app/Plugins/ClickUp/views/page-builder.blade.php ENDPATH**/ ?>