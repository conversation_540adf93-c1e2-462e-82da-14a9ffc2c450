@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-cog text-blue-600 mr-3"></i>
            Settings
        </h1>
        <p class="mt-2 text-gray-600">Configure your plugins and system preferences</p>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Error Messages -->
    @if($errors->any())
        <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission:</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Plugin Settings -->
    <div class="space-y-6">
        @forelse($pluginConfigs as $pluginName => $pluginData)
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $pluginData['plugin']->getName() }}</h3>
                            <p class="text-sm text-gray-600">{{ $pluginData['plugin']->getDescription() }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                v{{ $pluginData['plugin']->getVersion() }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $pluginData['plugin']->isEnabled() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $pluginData['plugin']->isEnabled() ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>
                </div>

                @if(!empty($pluginData['schema']))
                    <form method="POST" action="{{ route('settings.update') }}" class="p-6"
                          x-data="pluginSettings('{{ $pluginData['plugin']->getName() }}')"
                          x-init="init()">
                        @csrf
                        <input type="hidden" name="plugin" value="{{ $pluginData['plugin']->getName() }}">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($pluginData['schema'] as $fieldName => $field)
                                <div class="space-y-2">
                                    <label for="{{ $pluginName }}_{{ $fieldName }}" class="block text-sm font-medium text-gray-700">
                                        {{ $field['label'] }}
                                        @if($field['required'])
                                            <span class="text-red-500">*</span>
                                        @endif
                                    </label>
                                    
                                    @if($field['type'] === 'string')
                                        @if($fieldName === 'space_id' && $pluginName === 'clickup')
                                            <!-- Dynamic Space ID Dropdown for ClickUp -->
                                            <div class="space-y-2">
                                                <div x-show="loadingSpaces" class="flex items-center space-x-2">
                                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                                    <span class="text-sm text-gray-600">Loading spaces...</span>
                                                </div>

                                                <div x-show="!loadingSpaces && !spacesError && spaces.length > 0">
                                                    <select id="{{ $pluginName }}_{{ $fieldName }}"
                                                            name="config[{{ $fieldName }}]"
                                                            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                            @if($field['required']) required @endif>
                                                        <option value="">Select a space...</option>
                                                        <template x-for="space in spaces" :key="space.id">
                                                            <option :value="space.id"
                                                                    :selected="space.id === '{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? '') }}'"
                                                                    x-text="`${space.name} (${space.team_name})`"></option>
                                                        </template>
                                                    </select>
                                                </div>

                                                <div x-show="!loadingSpaces && (spacesError || spaces.length === 0)">
                                                    <div x-show="spacesError" class="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        <span x-text="spacesError"></span>
                                                    </div>
                                                    <input type="text"
                                                           id="{{ $pluginName }}_{{ $fieldName }}_fallback"
                                                           name="config[{{ $fieldName }}]"
                                                           value="{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '') }}"
                                                           placeholder="Enter Space ID manually"
                                                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                           @if($field['required']) required @endif>
                                                    <p class="mt-1 text-xs text-gray-500">
                                                        Unable to load spaces automatically. Please enter your Space ID manually.
                                                    </p>
                                                </div>
                                            </div>
                                        @else
                                            <input type="{{ isset($field['sensitive']) && $field['sensitive'] ? 'password' : 'text' }}"
                                                   id="{{ $pluginName }}_{{ $fieldName }}"
                                                   name="config[{{ $fieldName }}]"
                                                   value="{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '') }}"
                                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                                   @if($field['required']) required @endif>
                                        @endif
                                    @elseif($field['type'] === 'integer')
                                        <input type="number"
                                               id="{{ $pluginName }}_{{ $fieldName }}"
                                               name="config[{{ $fieldName }}]"
                                               value="{{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? '') }}"
                                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                               @if($field['required']) required @endif>
                                    @elseif($field['type'] === 'boolean')
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   id="{{ $pluginName }}_{{ $fieldName }}"
                                                   name="config[{{ $fieldName }}]"
                                                   value="1"
                                                   {{ old('config.' . $fieldName, $pluginData['config'][$fieldName] ?? $field['default'] ?? false) ? 'checked' : '' }}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="{{ $pluginName }}_{{ $fieldName }}" class="ml-2 block text-sm text-gray-900">
                                                Enable this option
                                            </label>
                                        </div>
                                    @endif
                                    
                                    @if(isset($field['description']))
                                        <p class="text-xs text-gray-500">{{ $field['description'] }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                                <i class="fas fa-save mr-2"></i>
                                Save Settings
                            </button>
                        </div>
                    </form>
                @else
                    <div class="p-6">
                        <p class="text-gray-500 text-center">This plugin has no configurable settings.</p>
                    </div>
                @endif
            </div>
        @empty
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-8 text-center">
                <i class="fas fa-plug text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Plugins Found</h3>
                <p class="text-gray-600">No plugins are currently installed or enabled.</p>
            </div>
        @endforelse
    </div>
</div>

@push('scripts')
<script>
function pluginSettings(pluginName) {
    return {
        pluginName: pluginName,
        loadingSpaces: false,
        spaces: [],
        spacesError: null,

        init() {
            if (this.pluginName.toLowerCase() === 'clickup') {
                this.loadSpaces();
            }
        },

        async loadSpaces() {
            this.loadingSpaces = true;
            this.spacesError = null;

            try {
                const response = await fetch('/api/plugins/clickup/spaces');
                const data = await response.json();

                if (data.success) {
                    this.spaces = data.spaces || [];
                    if (this.spaces.length === 0) {
                        this.spacesError = 'No spaces found. Please check your API token and permissions.';
                    }
                } else {
                    this.spacesError = data.message || 'Failed to load spaces from ClickUp API.';
                }
            } catch (error) {
                console.error('Error loading ClickUp spaces:', error);
                this.spacesError = 'Network error: Unable to connect to ClickUp API.';
            } finally {
                this.loadingSpaces = false;
            }
        }
    }
}
</script>
@endpush

@endsection
