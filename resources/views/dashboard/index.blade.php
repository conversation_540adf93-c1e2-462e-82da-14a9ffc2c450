@extends('layouts.app')

@section('content')
<div x-data="dashboard()" x-init="loadData()">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-tachometer-alt text-blue-600 mr-3"></i>
                    Product Intelligence Dashboard
                </h1>
                <p class="mt-2 text-gray-600">Monitor your product metrics and insights across all integrated systems</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Product Filter -->
                <div class="relative">
                    <select x-model="selectedProduct" @change="filterByProduct()" 
                            class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Products</option>
                        <option value="enterprise">Enterprise</option>
                        <option value="professional">Professional</option>
                        <option value="basic">Basic</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <i class="fas fa-chevron-down text-sm"></i>
                    </div>
                </div>
                <!-- Refresh Button -->
                <button @click="loadData()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-150 ease-in-out">
                    <i class="fas fa-sync-alt mr-2" :class="{ 'animate-spin': loading }"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Widgets Grid -->
    <div x-show="!loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- Active Clients Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Clients</p>
                    <p class="text-3xl font-bold text-gray-900" x-text="metrics.active_clients || 0"></p>
                </div>
                <div class="p-3 bg-green-100 rounded-full">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>12% from last month</span>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                    <p class="text-3xl font-bold text-gray-900">$<span x-text="(metrics.total_monthly_revenue || 0).toLocaleString()"></span></p>
                </div>
                <div class="p-3 bg-blue-100 rounded-full">
                    <i class="fas fa-dollar-sign text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-blue-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>8% from last month</span>
                </div>
            </div>
        </div>

        <!-- Churn Rate Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Churn Rate</p>
                    <p class="text-3xl font-bold text-gray-900"><span x-text="metrics.churn_rate || 0"></span>%</p>
                </div>
                <div class="p-3 bg-red-100 rounded-full">
                    <i class="fas fa-chart-line-down text-red-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-red-600">
                    <i class="fas fa-arrow-down mr-1"></i>
                    <span>2% from last month</span>
                </div>
            </div>
        </div>

        <!-- Leads Conversion Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Leads Conversion</p>
                    <p class="text-3xl font-bold text-gray-900"><span x-text="metrics.leads_conversion_rate || 0"></span>%</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-full">
                    <i class="fas fa-funnel-dollar text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-purple-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>5% from last month</span>
                </div>
            </div>
        </div>

        <!-- Revenue by Product Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:col-span-2">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Revenue by Product</h3>
                <i class="fas fa-chart-pie text-gray-400"></i>
            </div>
            <div class="space-y-3">
                <template x-for="(revenue, product) in metrics.revenue_by_product || {}" :key="product">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3" 
                                 :class="{
                                     'bg-blue-500': product === 'enterprise',
                                     'bg-green-500': product === 'professional', 
                                     'bg-yellow-500': product === 'basic'
                                 }"></div>
                            <span class="text-sm font-medium text-gray-700 capitalize" x-text="product"></span>
                        </div>
                        <span class="text-sm font-semibold text-gray-900">$<span x-text="revenue.toLocaleString()"></span></span>
                    </div>
                </template>
            </div>
        </div>

        <!-- ClickUp Task Progress Widget -->
        <div x-show="clickUpMetrics.task_progress" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">ClickUp Task Progress</h3>
                <i class="fab fa-clickup text-purple-600"></i>
            </div>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Completion Rate</span>
                    <span class="text-lg font-semibold text-green-600" x-text="(clickUpMetrics.task_progress?.completion_rate || 0) + '%'"></span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                         :style="`width: ${clickUpMetrics.task_progress?.completion_rate || 0}%`"></div>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" x-text="clickUpMetrics.task_progress?.completed_tasks || 0"></div>
                        <div class="text-xs text-gray-500">Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600" x-text="clickUpMetrics.task_progress?.in_progress_tasks || 0"></div>
                        <div class="text-xs text-gray-500">In Progress</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ClickUp Overdue Tasks Widget -->
        <div x-show="clickUpMetrics.overdue_count !== undefined" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Overdue Tasks</h3>
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold mb-2"
                     :class="(clickUpMetrics.overdue_count || 0) > 0 ? 'text-red-600' : 'text-green-600'"
                     x-text="clickUpMetrics.overdue_count || 0"></div>
                <div class="text-sm text-gray-600 mb-4">Tasks Overdue</div>
                <template x-if="clickUpMetrics.urgent_overdue && clickUpMetrics.urgent_overdue.length > 0">
                    <div class="space-y-2">
                        <div class="text-xs font-medium text-gray-700 mb-2">Most Urgent:</div>
                        <template x-for="task in clickUpMetrics.urgent_overdue.slice(0, 3)" :key="task.id">
                            <div class="text-xs bg-red-50 p-2 rounded border-l-2 border-red-400">
                                <div class="font-medium text-red-800" x-text="task.name"></div>
                                <div class="text-red-600" x-text="task.due_date ? new Date(parseInt(task.due_date)).toLocaleDateString() : 'No due date'"></div>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>

        <!-- ClickUp Team Productivity Widget -->
        <div x-show="clickUpMetrics.team_productivity" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:col-span-2">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Team Productivity</h3>
                <i class="fas fa-users text-blue-600"></i>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" x-text="clickUpMetrics.team_productivity?.weekly_completed || 0"></div>
                    <div class="text-sm text-gray-600">Completed This Week</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" x-text="clickUpMetrics.team_productivity?.monthly_completed || 0"></div>
                    <div class="text-sm text-gray-600">Completed This Month</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" x-text="(clickUpMetrics.team_productivity?.avg_completion_rate || 0) + '%'"></div>
                    <div class="text-sm text-gray-600">Avg Completion Rate</div>
                </div>
            </div>
            <template x-if="clickUpMetrics.team_productivity?.assignee_stats && clickUpMetrics.team_productivity.assignee_stats.length > 0">
                <div class="mt-4">
                    <div class="text-sm font-medium text-gray-700 mb-2">Top Performers:</div>
                    <div class="space-y-2">
                        <template x-for="assignee in clickUpMetrics.team_productivity.assignee_stats.slice(0, 3)" :key="assignee.name">
                            <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                <span class="text-sm font-medium" x-text="assignee.name"></span>
                                <span class="text-sm text-green-600" x-text="assignee.completion_rate + '%'"></span>
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </div>

        <!-- Plugin Status Widget -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Active Plugins</h3>
                <i class="fas fa-plug text-gray-400"></i>
            </div>
            <div class="space-y-2">
                <template x-for="plugin in plugins" :key="plugin.name">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-700" x-text="plugin.name"></span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              :class="plugin.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                            <span x-text="plugin.enabled ? 'Active' : 'Inactive'"></span>
                        </span>
                    </div>
                </template>
            </div>
        </div>

    </div>
</div>

@push('scripts')
<script>
function dashboard() {
    return {
        loading: false,
        selectedProduct: '',
        metrics: {},
        clickUpMetrics: {},
        plugins: [],

        async loadData() {
            this.loading = true;
            try {
                // Load dashboard data
                const dashboardResponse = await fetch('/api/dashboard');
                const dashboardData = await dashboardResponse.json();
                this.plugins = dashboardData.plugins || [];

                // Load TaqnyatAdmin metrics
                const metricsResponse = await fetch('/api/plugins/taqnyatadmin/metrics');
                const metricsData = await metricsResponse.json();
                this.metrics = metricsData.data || {};

                // Load ClickUp metrics if plugin is enabled
                if (this.plugins.ClickUp && this.plugins.ClickUp.enabled) {
                    try {
                        const clickUpResponse = await fetch('/api/plugins/clickup/metrics');
                        const clickUpData = await clickUpResponse.json();
                        if (clickUpData.success) {
                            this.clickUpMetrics = clickUpData.data || {};
                        } else {
                            console.warn('ClickUp API error:', clickUpData.error);
                            this.clickUpMetrics = {};
                        }
                    } catch (clickUpError) {
                        console.warn('Failed to load ClickUp data:', clickUpError);
                        this.clickUpMetrics = {};
                    }
                }

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            } finally {
                this.loading = false;
            }
        },
        
        filterByProduct() {
            // Implement product filtering logic here
            console.log('Filtering by product:', this.selectedProduct);
        }
    }
}
</script>
@endpush
@endsection
